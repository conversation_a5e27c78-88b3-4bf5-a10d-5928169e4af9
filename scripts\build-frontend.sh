#!/bin/bash
# Script to build the frontend for either development or production

# Default to development if no environment is specified
ENVIRONMENT=${1:-development}

# Navigate to client directory
cd client

# Install dependencies if node_modules doesn't exist
if [ ! -d "node_modules" ]; then
  echo "Installing dependencies..."
  npm install
fi

# Build for the specified environment
if [ "$ENVIRONMENT" == "production" ]; then
  echo "Building for PRODUCTION environment..."

  # Set production environment variables
  export NODE_ENV=production
  export REACT_APP_ENVIRONMENT=production
  export REACT_APP_CART_API_ROUTE=https://api.thealpinestudio.com/test-checkout-products
  export REACT_APP_PRODUCT_API_ROUTE=https://api.thealpinestudio.com/test-list-products
  export REACT_APP_CONTACT_API_ROUTE=https://api.thealpinestudio.com/test-contact-email
  export REACT_APP_WEBHOOKS_API_ROUTE=https://api.thealpinestudio.com/test-webhooks
  export REACT_APP_STRIPE_PUBLIC_KEY=pk_live_51NirT5H1KC3YYzQbtb2YFUchsLWDDLZ42Wx8qj81U89hFgcFEP540ytEyuLChzCQt1U5zuxn1rsBMrrJRETiTBvF000sAqnpMS
  export REACT_APP_S3_BUCKET=thealpinestudio.com

  # Build for production
  npm run build:prod

  # Deploy to production S3 bucket
  echo "Deploying to production S3 bucket..."
  aws s3 cp build/ s3://thealpinestudio.com/ --recursive

  # Invalidate production CloudFront cache
  echo "Invalidating production CloudFront cache..."
  aws cloudfront create-invalidation --distribution-id ETN10ORXOMMDL --paths "/*"

else
  echo "Building for DEVELOPMENT environment..."

  # Set development environment variables
  export NODE_ENV=development
  export REACT_APP_ENVIRONMENT=development
  export REACT_APP_CART_API_ROUTE=https://u3vj4dkndh.execute-api.us-west-2.amazonaws.com/test/dev-checkout-products
  export REACT_APP_PRODUCT_API_ROUTE=https://u3vj4dkndh.execute-api.us-west-2.amazonaws.com/test/dev-list-products
  export REACT_APP_CONTACT_API_ROUTE=https://u3vj4dkndh.execute-api.us-west-2.amazonaws.com/test/dev-contact-email
  export REACT_APP_WEBHOOKS_API_ROUTE=https://u3vj4dkndh.execute-api.us-west-2.amazonaws.com/test/dev-webhooks
  export REACT_APP_STRIPE_PUBLIC_KEY=pk_test_51NirT5H1KC3YYzQbwoT6Tewe6rFzhf3e5xmWrgIw75V01q126czeifShPGVOSf00K8Pblw7FUA5SaBEXfW5VnXb700uS8rSOyL
  export REACT_APP_S3_BUCKET=dev-thealpinestudio-hosting

  # Build for development
  npm run build:dev

  # Deploy to development S3 bucket
  echo "Deploying to development S3 bucket..."
  aws s3 cp build/ s3://test.thealpinestudio.com-v1/ --recursive

  # Invalidate development CloudFront cache
  echo "Invalidating development CloudFront cache..."
  aws cloudfront create-invalidation --distribution-id E333R3CHKXLYGZ --paths "/*"
fi

echo "Build and deployment completed successfully!"
