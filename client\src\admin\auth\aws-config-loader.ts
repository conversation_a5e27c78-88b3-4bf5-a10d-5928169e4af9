// Import environment-specific configurations
import * as devConfig from './aws-config.dev';
import * as prodConfig from './aws-config.prod';

// Determine which configuration to use based on environment
const isProduction = window.REACT_APP_ENVIRONMENT === 'production';
const config = isProduction ? prodConfig : devConfig;

// Export all configuration objects
export const {
  configLogin,
  configPass,
  configVerify,
  configPreSignup,
  configPostConfirmation,
  configSignup,
  configConfirmSignup,
  configTotp,
  configRefreshToken,
  configUploadProduct,
  configFetchProducts,
  configMarkProductAsSold,
  configDeleteProduct,
  configEditProduct,
  configFetchProductById,
  configPreSignUrl,
  configTestListProducts,
  configStripeCheckout,
  configContactForm
} = config;

// Export a helper function to get the current environment
export const getCurrentEnvironment = () => {
  return isProduction ? 'production' : 'development';
};

// Log the current environment for debugging
console.log(`Using ${getCurrentEnvironment()} API endpoints`);
