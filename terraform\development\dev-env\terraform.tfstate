{"version": 4, "terraform_version": "1.10.5", "serial": 352, "lineage": "53414a2b-5148-fd68-3224-a4ef435ede6b", "outputs": {"api_gateway_arn": {"value": "arn:aws:apigateway:us-west-2::/restapis/u3vj4dkndh", "type": "string"}, "api_gateway_id": {"value": "u3vj4dkndh", "type": "string"}, "api_gateway_url": {"value": "https://u3vj4dkndh.execute-api.us-west-2.amazonaws.com/test", "type": "string"}, "bucket_name": {"value": {"acceleration_status": "", "acl": null, "arn": "arn:aws:s3:::dev-thealpinestudio-hosting-v1", "bucket": "dev-thealpinestudio-hosting-v1", "bucket_domain_name": "dev-thealpinestudio-hosting-v1.s3.amazonaws.com", "bucket_prefix": "", "bucket_regional_domain_name": "dev-thealpinestudio-hosting-v1.s3.us-west-2.amazonaws.com", "cors_rule": [{"allowed_headers": [], "allowed_methods": ["POST", "GET", "PUT"], "allowed_origins": ["https://test.thealpinestudio.com", "https://d33jwd5p0tdk6v.cloudfront.net"], "expose_headers": [], "max_age_seconds": 0}, {"allowed_headers": ["*"], "allowed_methods": ["POST", "GET", "PUT"], "allowed_origins": ["https://test.thealpinestudio.com", "https://d33jwd5p0tdk6v.cloudfront.net"], "expose_headers": ["ETag"], "max_age_seconds": 3000}], "force_destroy": false, "grant": [{"id": "", "permissions": ["READ"], "type": "Group", "uri": "http://acs.amazonaws.com/groups/global/AllUsers"}, {"id": "49f744bf851c1df61a3ea6002103b48141213c5416dac9ce3b0d5bd8a58045e4", "permissions": ["FULL_CONTROL"], "type": "CanonicalUser", "uri": ""}], "hosted_zone_id": "Z3BJ6K6RIION7M", "id": "dev-thealpinestudio-hosting-v1", "lifecycle_rule": [], "logging": [], "object_lock_configuration": [], "object_lock_enabled": false, "policy": "{\"Statement\":[{\"Action\":[\"s3:GetObject\",\"s3:PutObject\"],\"Effect\":\"Allow\",\"Principal\":\"*\",\"Resource\":\"arn:aws:s3:::dev-thealpinestudio-hosting-v1/*\",\"Sid\":\"PublicRead\"}],\"Version\":\"2012-10-17\"}", "region": "us-west-2", "replication_configuration": [], "request_payer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "server_side_encryption_configuration": [{"rule": [{"apply_server_side_encryption_by_default": [{"kms_master_key_id": "", "sse_algorithm": "AES256"}], "bucket_key_enabled": false}]}], "tags": {"Environment": "development"}, "tags_all": {"Environment": "development"}, "timeouts": null, "versioning": [{"enabled": false, "mfa_delete": false}], "website": [{"error_document": "error.html", "index_document": "index.html", "redirect_all_requests_to": "", "routing_rules": ""}], "website_domain": "s3-website-us-west-2.amazonaws.com", "website_endpoint": "dev-thealpinestudio-hosting-v1.s3-website-us-west-2.amazonaws.com"}, "type": ["object", {"acceleration_status": "string", "acl": "string", "arn": "string", "bucket": "string", "bucket_domain_name": "string", "bucket_prefix": "string", "bucket_regional_domain_name": "string", "cors_rule": ["list", ["object", {"allowed_headers": ["list", "string"], "allowed_methods": ["list", "string"], "allowed_origins": ["list", "string"], "expose_headers": ["list", "string"], "max_age_seconds": "number"}]], "force_destroy": "bool", "grant": ["set", ["object", {"id": "string", "permissions": ["set", "string"], "type": "string", "uri": "string"}]], "hosted_zone_id": "string", "id": "string", "lifecycle_rule": ["list", ["object", {"abort_incomplete_multipart_upload_days": "number", "enabled": "bool", "expiration": ["list", ["object", {"date": "string", "days": "number", "expired_object_delete_marker": "bool"}]], "id": "string", "noncurrent_version_expiration": ["list", ["object", {"days": "number"}]], "noncurrent_version_transition": ["set", ["object", {"days": "number", "storage_class": "string"}]], "prefix": "string", "tags": ["map", "string"], "transition": ["set", ["object", {"date": "string", "days": "number", "storage_class": "string"}]]}]], "logging": ["list", ["object", {"target_bucket": "string", "target_prefix": "string"}]], "object_lock_configuration": ["list", ["object", {"object_lock_enabled": "string", "rule": ["list", ["object", {"default_retention": ["list", ["object", {"days": "number", "mode": "string", "years": "number"}]]}]]}]], "object_lock_enabled": "bool", "policy": "string", "region": "string", "replication_configuration": ["list", ["object", {"role": "string", "rules": ["set", ["object", {"delete_marker_replication_status": "string", "destination": ["list", ["object", {"access_control_translation": ["list", ["object", {"owner": "string"}]], "account_id": "string", "bucket": "string", "metrics": ["list", ["object", {"minutes": "number", "status": "string"}]], "replica_kms_key_id": "string", "replication_time": ["list", ["object", {"minutes": "number", "status": "string"}]], "storage_class": "string"}]], "filter": ["list", ["object", {"prefix": "string", "tags": ["map", "string"]}]], "id": "string", "prefix": "string", "priority": "number", "source_selection_criteria": ["list", ["object", {"sse_kms_encrypted_objects": ["list", ["object", {"enabled": "bool"}]]}]], "status": "string"}]]}]], "request_payer": "string", "server_side_encryption_configuration": ["list", ["object", {"rule": ["list", ["object", {"apply_server_side_encryption_by_default": ["list", ["object", {"kms_master_key_id": "string", "sse_algorithm": "string"}]], "bucket_key_enabled": "bool"}]]}]], "tags": ["map", "string"], "tags_all": ["map", "string"], "timeouts": ["object", {"create": "string", "delete": "string", "read": "string", "update": "string"}], "versioning": ["list", ["object", {"enabled": "bool", "mfa_delete": "bool"}]], "website": ["list", ["object", {"error_document": "string", "index_document": "string", "redirect_all_requests_to": "string", "routing_rules": "string"}]], "website_domain": "string", "website_endpoint": "string"}]}, "cognito_app_client_id": {"value": "5v5dt6ikaea3i6tlct1t7i22ad", "type": "string"}, "cognito_client_id": {"value": "5v5dt6ikaea3i6tlct1t7i22ad", "type": "string"}, "cognito_provider_name": {"value": "https://cognito-idp.us-west-2.amazonaws.com/us-west-2_WD5hr07nW", "type": "string"}, "cognito_user_pool_id": {"value": "us-west-2_WD5hr07nW", "type": "string"}, "lambda_function_arn": {"value": "arn:aws:apigateway:us-west-2:lambda:path/2015-03-31/functions/arn:aws:lambda:us-west-2:************:function:dev-list-products/invocations", "type": "string"}}, "resources": [{"module": "module.api_gateway", "mode": "data", "type": "aws_caller_identity", "name": "current", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"account_id": "************", "arn": "arn:aws:iam::************:user/ariel<PERSON><PERSON><PERSON>z", "id": "************", "user_id": "AIDAV7EONF4B2GJKP2UPS"}, "sensitive_attributes": []}]}, {"module": "module.api_gateway", "mode": "data", "type": "aws_region", "name": "current", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"description": "US West (Oregon)", "endpoint": "ec2.us-west-2.amazonaws.com", "id": "us-west-2", "name": "us-west-2"}, "sensitive_attributes": []}]}, {"module": "module.api_gateway", "mode": "data", "type": "aws_route53_zone", "name": "zone", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:route53:::hostedzone/Z04085477NKG6FGRMKK4", "caller_reference": "1b7bf4fd-6b65-41bd-8b92-d13ad9a8651b", "comment": "thealpinestudio art site", "id": "Z04085477NKG6FGRMKK4", "linked_service_description": null, "linked_service_principal": null, "name": "thealpinestudio.com", "name_servers": ["ns-396.awsdns-49.com", "ns-864.awsdns-44.net", "ns-1524.awsdns-62.org", "ns-1721.awsdns-23.co.uk"], "primary_name_server": "ns-396.awsdns-49.com", "private_zone": false, "resource_record_set_count": 35, "tags": {}, "vpc_id": null, "zone_id": "Z04085477NKG6FGRMKK4"}, "sensitive_attributes": []}]}, {"module": "module.api_gateway", "mode": "managed", "type": "aws_acm_certificate", "name": "certificate", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:acm:us-west-2:************:certificate/00cfe049-926a-42b2-99d0-d93b890812f3", "certificate_authority_arn": "", "certificate_body": null, "certificate_chain": null, "domain_name": "api.dev.thealpinestudio.com", "domain_validation_options": [{"domain_name": "api.dev.thealpinestudio.com", "resource_record_name": "_268127fdfeb6b1f766c314e837ddd2bf.api.dev.thealpinestudio.com.", "resource_record_type": "CNAME", "resource_record_value": "_522ec28f3aaa52e25a290fc2f7a8bbe9.xlfgrmvvlj.acm-validations.aws."}], "early_renewal_duration": "", "id": "arn:aws:acm:us-west-2:************:certificate/00cfe049-926a-42b2-99d0-d93b890812f3", "key_algorithm": "RSA_2048", "not_after": "2026-06-20T23:59:59Z", "not_before": "2025-05-22T00:00:00Z", "options": [{"certificate_transparency_logging_preference": "ENABLED"}], "pending_renewal": false, "private_key": null, "renewal_eligibility": "ELIGIBLE", "renewal_summary": [], "status": "ISSUED", "subject_alternative_names": ["api.dev.thealpinestudio.com"], "tags": {}, "tags_all": {}, "type": "AMAZON_ISSUED", "validation_emails": [], "validation_method": "DNS", "validation_option": []}, "sensitive_attributes": [[{"type": "get_attr", "value": "private_key"}]], "private": "bnVsbA=="}]}, {"module": "module.api_gateway", "mode": "managed", "type": "aws_acm_certificate_validation", "name": "certificate_validation", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"certificate_arn": "arn:aws:acm:us-west-2:************:certificate/00cfe049-926a-42b2-99d0-d93b890812f3", "id": "2025-05-22 16:41:50.687 +0000 UTC", "timeouts": null, "validation_record_fqdns": ["_268127fdfeb6b1f766c314e837ddd2bf.api.dev.thealpinestudio.com"]}, "sensitive_attributes": [], "private": "****************************************************************************************", "dependencies": ["module.api_gateway.aws_acm_certificate.certificate", "module.api_gateway.aws_route53_record.certificate_validation", "module.api_gateway.data.aws_route53_zone.zone"]}]}, {"module": "module.api_gateway", "mode": "managed", "type": "aws_api_gateway_account", "name": "rest_api_account", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"api_key_version": "4", "cloudwatch_role_arn": "arn:aws:iam::************:role/api_gateway_cloudwatch_logs-dev", "features": ["UsagePlans"], "id": "api-gateway-account", "throttle_settings": [{"burst_limit": 5000, "rate_limit": 10000}]}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["module.api_gateway.aws_iam_role.api_gateway_cloudwatch_logs"]}]}, {"module": "module.api_gateway", "mode": "managed", "type": "aws_api_gateway_authorizer", "name": "api_authorizer", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:apigateway:us-west-2::/restapis/u3vj4dkndh/authorizers/8vwwvd", "authorizer_credentials": "", "authorizer_result_ttl_in_seconds": 300, "authorizer_uri": "", "id": "8vwwvd", "identity_source": "method.request.header.Authorization", "identity_validation_expression": "", "name": "CognitoUserPoolAuthorizerapi", "provider_arns": ["arn:aws:cognito-idp:us-west-2:************:userpool/us-west-2_WD5hr07nW"], "rest_api_id": "u3vj4dkndh", "type": "COGNITO_USER_POOLS"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["module.api_gateway.aws_api_gateway_rest_api.rest_api", "module.cognito.aws_cognito_user_pool.user_pool"], "create_before_destroy": true}]}, {"module": "module.api_gateway", "mode": "managed", "type": "aws_api_gateway_deployment", "name": "rest_api_deployment", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"created_date": "2025-05-22T17:06:55Z", "description": "", "execution_arn": "arn:aws:execute-api:us-west-2:************:u3vj4dkndh/", "id": "j2b7gu", "invoke_url": "https://u3vj4dkndh.execute-api.us-west-2.amazonaws.com/", "rest_api_id": "u3vj4dkndh", "stage_description": null, "stage_name": null, "triggers": {"redeployment": "5d866cf7a67820bd70cf6cfd76a08b6c4a528053"}, "variables": null}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["module.api_gateway.aws_api_gateway_authorizer.api_authorizer", "module.api_gateway.aws_api_gateway_integration.checkout_products_post_integration", "module.api_gateway.aws_api_gateway_integration.contact_form_post_integration", "module.api_gateway.aws_api_gateway_integration.cors_integration", "module.api_gateway.aws_api_gateway_integration.rest_api_get_method_integration", "module.api_gateway.aws_api_gateway_integration.rest_api_post_method_integration", "module.api_gateway.aws_api_gateway_integration.webhooks_products_post_integration", "module.api_gateway.aws_api_gateway_method.checkout_products_post_method", "module.api_gateway.aws_api_gateway_method.contact_form_post_method", "module.api_gateway.aws_api_gateway_method.cors_method", "module.api_gateway.aws_api_gateway_method.rest_api_get_method", "module.api_gateway.aws_api_gateway_method.rest_api_post_method", "module.api_gateway.aws_api_gateway_method.webhooks_products_post_method", "module.api_gateway.aws_api_gateway_resource.checkout_products_resource", "module.api_gateway.aws_api_gateway_resource.contact_resource", "module.api_gateway.aws_api_gateway_resource.rest_api_resource", "module.api_gateway.aws_api_gateway_resource.webhooks_resource", "module.api_gateway.aws_api_gateway_rest_api.rest_api", "module.api_gateway.aws_lambda_permission.lambda_api_gw_permissions_checkout", "module.api_gateway.aws_lambda_permission.lambda_api_gw_permissions_list_products_get", "module.api_gateway.aws_lambda_permission.lambda_api_gw_permissions_list_products_post", "module.cognito.aws_cognito_user_pool.user_pool", "module.lambda_function.aws_iam_role.lambda_execution_role", "module.lambda_function.aws_iam_role.lambda_execution_role_get", "module.lambda_function.aws_lambda_function.checkout_products_function", "module.lambda_function.aws_lambda_function.contact_email_function", "module.lambda_function.aws_lambda_function.list_products_function", "module.lambda_function.aws_lambda_function.webhooks_function", "module.lambda_function.aws_s3_object.lambda_code", "module.lambda_function.aws_s3_object.lambda_code_checkout", "module.lambda_function.aws_s3_object.lambda_code_contact", "module.lambda_function.aws_s3_object.lambda_code_webhooks", "module.lambda_function.data.archive_file.lambda_code_checkout", "module.lambda_function.data.archive_file.lambda_code_contact", "module.lambda_function.data.archive_file.lambda_code_list", "module.lambda_function.data.archive_file.lambda_code_webhooks"], "create_before_destroy": true}]}, {"module": "module.api_gateway", "mode": "managed", "type": "aws_api_gateway_domain_name", "name": "api_domain", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:apigateway:us-west-2::/domainnames/api.dev.thealpinestudio.com", "certificate_arn": "", "certificate_body": null, "certificate_chain": null, "certificate_name": "", "certificate_private_key": null, "certificate_upload_date": "2025-05-22T16:42:14Z", "cloudfront_domain_name": "", "cloudfront_zone_id": "Z2FDTNDATAQYW2", "domain_name": "api.dev.thealpinestudio.com", "endpoint_configuration": [{"types": ["REGIONAL"]}], "id": "api.dev.thealpinestudio.com", "mutual_tls_authentication": [], "ownership_verification_certificate_arn": "", "regional_certificate_arn": "arn:aws:acm:us-west-2:************:certificate/00cfe049-926a-42b2-99d0-d93b890812f3", "regional_certificate_name": "", "regional_domain_name": "d-lxsosg4up5.execute-api.us-west-2.amazonaws.com", "regional_zone_id": "Z2OJLYMUO9EFXC", "security_policy": "TLS_1_2", "tags": {}, "tags_all": {}}, "sensitive_attributes": [[{"type": "get_attr", "value": "certificate_private_key"}]], "private": "bnVsbA==", "dependencies": ["module.api_gateway.aws_acm_certificate.certificate", "module.api_gateway.aws_acm_certificate_validation.certificate_validation", "module.api_gateway.aws_route53_record.certificate_validation", "module.api_gateway.data.aws_route53_zone.zone"]}]}, {"module": "module.api_gateway", "mode": "managed", "type": "aws_api_gateway_gateway_response", "name": "response_4xx", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"id": "aggr-u3vj4dkndh-DEFAULT_4XX", "response_parameters": {"gatewayresponse.header.Access-Control-Allow-Origin": "'https://test.thealpinestudio.com'"}, "response_templates": {"application/json": "{'message':$context.error.messageString}"}, "response_type": "DEFAULT_4XX", "rest_api_id": "u3vj4dkndh", "status_code": ""}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["module.api_gateway.aws_api_gateway_rest_api.rest_api"]}]}, {"module": "module.api_gateway", "mode": "managed", "type": "aws_api_gateway_gateway_response", "name": "response_5xx", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"id": "aggr-u3vj4dkndh-DEFAULT_5XX", "response_parameters": {"gatewayresponse.header.Access-Control-Allow-Origin": "'https://test.thealpinestudio.com'"}, "response_templates": {"application/json": "{'message':$context.error.messageString}"}, "response_type": "DEFAULT_5XX", "rest_api_id": "u3vj4dkndh", "status_code": ""}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["module.api_gateway.aws_api_gateway_rest_api.rest_api"]}]}, {"module": "module.api_gateway", "mode": "managed", "type": "aws_api_gateway_integration", "name": "checkout_products_post_integration", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"cache_key_parameters": null, "cache_namespace": "77t199", "connection_id": "", "connection_type": "INTERNET", "content_handling": "", "credentials": "", "http_method": "POST", "id": "agi-u3vj4dkndh-77t199-POST", "integration_http_method": "POST", "passthrough_behavior": "WHEN_NO_MATCH", "request_parameters": null, "request_templates": null, "resource_id": "77t199", "rest_api_id": "u3vj4dkndh", "timeout_milliseconds": 29000, "tls_config": [], "type": "AWS_PROXY", "uri": "arn:aws:apigateway:us-west-2:lambda:path/2015-03-31/functions/arn:aws:lambda:us-west-2:************:function:dev-checkout-products/invocations"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["module.api_gateway.aws_api_gateway_method.checkout_products_post_method", "module.api_gateway.aws_api_gateway_resource.checkout_products_resource", "module.api_gateway.aws_api_gateway_rest_api.rest_api", "module.api_gateway.aws_lambda_permission.lambda_api_gw_permissions_checkout", "module.lambda_function.aws_iam_role.lambda_execution_role", "module.lambda_function.aws_lambda_function.checkout_products_function", "module.lambda_function.aws_s3_object.lambda_code_checkout", "module.lambda_function.data.archive_file.lambda_code_checkout"], "create_before_destroy": true}]}, {"module": "module.api_gateway", "mode": "managed", "type": "aws_api_gateway_integration", "name": "contact_form_post_integration", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"cache_key_parameters": null, "cache_namespace": "m019lh", "connection_id": "", "connection_type": "INTERNET", "content_handling": "", "credentials": "", "http_method": "POST", "id": "agi-u3vj4dkndh-m019lh-POST", "integration_http_method": "POST", "passthrough_behavior": "WHEN_NO_MATCH", "request_parameters": null, "request_templates": {"application/json": "    {\r\n      \"method\": \"$context.httpMethod\",\r\n      \"body\" : $input.json('$'),\r\n      \"headers\": {\r\n        #foreach($param in $input.params().header.keySet())\r\n        \"$param\": \"$util.escapeJavaScript($input.params().header.get($param))\"\r\n        #if($foreach.hasNext),#end\r\n        #end\r\n      },\r\n      \"queryStringParameters\": {\r\n        #foreach($param in $input.params().querystring.keySet())\r\n        \"$param\": \"$util.escapeJavaScript($input.params().querystring.get($param))\"\r\n        #if($foreach.hasNext),#end\r\n        #end\r\n      }\r\n    }\r\n"}, "resource_id": "m019lh", "rest_api_id": "u3vj4dkndh", "timeout_milliseconds": 29000, "tls_config": [], "type": "AWS_PROXY", "uri": "arn:aws:apigateway:us-west-2:lambda:path/2015-03-31/functions/arn:aws:lambda:us-west-2:************:function:dev-contact-email/invocations"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["module.api_gateway.aws_api_gateway_authorizer.api_authorizer", "module.api_gateway.aws_api_gateway_method.contact_form_post_method", "module.api_gateway.aws_api_gateway_resource.contact_resource", "module.api_gateway.aws_api_gateway_rest_api.rest_api", "module.cognito.aws_cognito_user_pool.user_pool", "module.lambda_function.aws_iam_role.lambda_execution_role", "module.lambda_function.aws_lambda_function.contact_email_function", "module.lambda_function.aws_s3_object.lambda_code_contact", "module.lambda_function.data.archive_file.lambda_code_contact"], "create_before_destroy": true}]}, {"module": "module.api_gateway", "mode": "managed", "type": "aws_api_gateway_integration", "name": "cors_integration", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"cache_key_parameters": [], "cache_namespace": "u9qm9y", "connection_id": "", "connection_type": "INTERNET", "content_handling": "", "credentials": "", "http_method": "OPTIONS", "id": "agi-u3vj4dkndh-u9qm9y-OPTIONS", "integration_http_method": "", "passthrough_behavior": "NEVER", "request_parameters": {}, "request_templates": {"application/json": "{\"statusCode\":200}"}, "resource_id": "u9qm9y", "rest_api_id": "u3vj4dkndh", "timeout_milliseconds": 29000, "tls_config": [], "type": "MOCK", "uri": ""}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["module.api_gateway.aws_api_gateway_method.cors_method", "module.api_gateway.aws_api_gateway_resource.rest_api_resource", "module.api_gateway.aws_api_gateway_rest_api.rest_api"], "create_before_destroy": true}]}, {"module": "module.api_gateway", "mode": "managed", "type": "aws_api_gateway_integration", "name": "rest_api_get_method_integration", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"cache_key_parameters": null, "cache_namespace": "u9qm9y", "connection_id": "", "connection_type": "INTERNET", "content_handling": "", "credentials": "", "http_method": "GET", "id": "agi-u3vj4dkndh-u9qm9y-GET", "integration_http_method": "POST", "passthrough_behavior": "WHEN_NO_MATCH", "request_parameters": null, "request_templates": {"application/json": "    {\r\n      \"method\": \"$context.httpMethod\",\r\n      \"body\" : $input.json('$'),\r\n      \"headers\": {\r\n        #foreach($param in $input.params().header.keySet())\r\n        \"$param\": \"$util.escapeJavaScript($input.params().header.get($param))\"\r\n        #if($foreach.hasNext),#end\r\n        #end\r\n      },\r\n      \"queryStringParameters\": {\r\n        #foreach($param in $input.params().querystring.keySet())\r\n        \"$param\": \"$util.escapeJavaScript($input.params().querystring.get($param))\"\r\n        #if($foreach.hasNext),#end\r\n        #end\r\n      }\r\n    }\r\n"}, "resource_id": "u9qm9y", "rest_api_id": "u3vj4dkndh", "timeout_milliseconds": 29000, "tls_config": [], "type": "AWS_PROXY", "uri": "arn:aws:apigateway:us-west-2:lambda:path/2015-03-31/functions/arn:aws:lambda:us-west-2:************:function:dev-list-products/invocations"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["module.api_gateway.aws_api_gateway_method.rest_api_get_method", "module.api_gateway.aws_api_gateway_resource.rest_api_resource", "module.api_gateway.aws_api_gateway_rest_api.rest_api", "module.api_gateway.aws_lambda_permission.lambda_api_gw_permissions_list_products_get", "module.lambda_function.aws_iam_role.lambda_execution_role_get", "module.lambda_function.aws_lambda_function.list_products_function", "module.lambda_function.aws_s3_object.lambda_code", "module.lambda_function.data.archive_file.lambda_code_list"], "create_before_destroy": true}]}, {"module": "module.api_gateway", "mode": "managed", "type": "aws_api_gateway_integration", "name": "rest_api_post_method_integration", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"cache_key_parameters": null, "cache_namespace": "u9qm9y", "connection_id": "", "connection_type": "INTERNET", "content_handling": "", "credentials": "", "http_method": "POST", "id": "agi-u3vj4dkndh-u9qm9y-POST", "integration_http_method": "POST", "passthrough_behavior": "WHEN_NO_MATCH", "request_parameters": null, "request_templates": {"application/json": "    {\r\n      \"method\": \"$context.httpMethod\",\r\n      \"body\" : $input.json('$'),\r\n      \"headers\": {\r\n        #foreach($param in $input.params().header.keySet())\r\n        \"$param\": \"$util.escapeJavaScript($input.params().header.get($param))\"\r\n        #if($foreach.hasNext),#end\r\n        #end\r\n      },\r\n      \"queryStringParameters\": {\r\n        #foreach($param in $input.params().querystring.keySet())\r\n        \"$param\": \"$util.escapeJavaScript($input.params().querystring.get($param))\"\r\n        #if($foreach.hasNext),#end\r\n        #end\r\n      }\r\n    }\r\n"}, "resource_id": "u9qm9y", "rest_api_id": "u3vj4dkndh", "timeout_milliseconds": 29000, "tls_config": [], "type": "AWS_PROXY", "uri": "arn:aws:apigateway:us-west-2:lambda:path/2015-03-31/functions/arn:aws:lambda:us-west-2:************:function:dev-list-products/invocations"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["module.api_gateway.aws_api_gateway_method.rest_api_post_method", "module.api_gateway.aws_api_gateway_resource.rest_api_resource", "module.api_gateway.aws_api_gateway_rest_api.rest_api", "module.api_gateway.aws_lambda_permission.lambda_api_gw_permissions_list_products_post", "module.lambda_function.aws_iam_role.lambda_execution_role_get", "module.lambda_function.aws_lambda_function.list_products_function", "module.lambda_function.aws_s3_object.lambda_code", "module.lambda_function.data.archive_file.lambda_code_list"], "create_before_destroy": true}]}, {"module": "module.api_gateway", "mode": "managed", "type": "aws_api_gateway_integration", "name": "webhooks_products_post_integration", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"cache_key_parameters": null, "cache_namespace": "2xyt9t", "connection_id": "", "connection_type": "INTERNET", "content_handling": "", "credentials": "", "http_method": "POST", "id": "agi-u3vj4dkndh-2xyt9t-POST", "integration_http_method": "POST", "passthrough_behavior": "WHEN_NO_MATCH", "request_parameters": null, "request_templates": null, "resource_id": "2xyt9t", "rest_api_id": "u3vj4dkndh", "timeout_milliseconds": 29000, "tls_config": [], "type": "AWS_PROXY", "uri": "arn:aws:apigateway:us-west-2:lambda:path/2015-03-31/functions/arn:aws:lambda:us-west-2:************:function:dev-webhooks/invocations"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["module.api_gateway.aws_api_gateway_method.webhooks_products_post_method", "module.api_gateway.aws_api_gateway_resource.webhooks_resource", "module.api_gateway.aws_api_gateway_rest_api.rest_api", "module.lambda_function.aws_iam_role.lambda_execution_role", "module.lambda_function.aws_lambda_function.webhooks_function", "module.lambda_function.aws_s3_object.lambda_code_webhooks", "module.lambda_function.data.archive_file.lambda_code_webhooks"], "create_before_destroy": true}]}, {"module": "module.api_gateway", "mode": "managed", "type": "aws_api_gateway_integration_response", "name": "checkout_products_post_integration_response_200", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"content_handling": "", "http_method": "POST", "id": "agir-u3vj4dkndh-77t199-POST-200", "resource_id": "77t199", "response_parameters": {"method.response.header.Access-Control-Allow-Credentials": "'true'", "method.response.header.Access-Control-Allow-Headers": "'Content-Type, X-Amz-Date, Authorization, X-Api-Key, X-Amz-Security-Token,X-Requested-With'", "method.response.header.Access-Control-Allow-Methods": "'OPTIONS,POST'", "method.response.header.Access-Control-Allow-Origin": "'https://test.thealpinestudio.com'"}, "response_templates": null, "rest_api_id": "u3vj4dkndh", "selection_pattern": "", "status_code": "200"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["module.api_gateway.aws_api_gateway_integration.checkout_products_post_integration", "module.api_gateway.aws_api_gateway_method.checkout_products_post_method", "module.api_gateway.aws_api_gateway_method_response.checkout_products_post_method_response_200", "module.api_gateway.aws_api_gateway_resource.checkout_products_resource", "module.api_gateway.aws_api_gateway_rest_api.rest_api", "module.api_gateway.aws_lambda_permission.lambda_api_gw_permissions_checkout", "module.lambda_function.aws_iam_role.lambda_execution_role", "module.lambda_function.aws_lambda_function.checkout_products_function", "module.lambda_function.aws_s3_object.lambda_code_checkout", "module.lambda_function.data.archive_file.lambda_code_checkout"]}]}, {"module": "module.api_gateway", "mode": "managed", "type": "aws_api_gateway_integration_response", "name": "contact_post_integration_response_200", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"content_handling": "", "http_method": "POST", "id": "agir-u3vj4dkndh-m019lh-POST-200", "resource_id": "m019lh", "response_parameters": {"method.response.header.Access-Control-Allow-Credentials": "'true'", "method.response.header.Access-Control-Allow-Headers": "'Content-Type, X-Amz-Date, Authorization, X-Api-Key, X-Amz-Security-Token,X-Requested-With'", "method.response.header.Access-Control-Allow-Methods": "'OPTIONS,POST'", "method.response.header.Access-Control-Allow-Origin": "'https://test.thealpinestudio.com'"}, "response_templates": null, "rest_api_id": "u3vj4dkndh", "selection_pattern": "", "status_code": "200"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["module.api_gateway.aws_api_gateway_authorizer.api_authorizer", "module.api_gateway.aws_api_gateway_integration.contact_form_post_integration", "module.api_gateway.aws_api_gateway_method.contact_form_post_method", "module.api_gateway.aws_api_gateway_method_response.contact_post_method_response_200", "module.api_gateway.aws_api_gateway_resource.contact_resource", "module.api_gateway.aws_api_gateway_rest_api.rest_api", "module.cognito.aws_cognito_user_pool.user_pool", "module.lambda_function.aws_iam_role.lambda_execution_role", "module.lambda_function.aws_lambda_function.contact_email_function", "module.lambda_function.aws_s3_object.lambda_code_contact", "module.lambda_function.data.archive_file.lambda_code_contact"]}]}, {"module": "module.api_gateway", "mode": "managed", "type": "aws_api_gateway_integration_response", "name": "cors_integration_response", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"content_handling": "", "http_method": "OPTIONS", "id": "agir-u3vj4dkndh-u9qm9y-OPTIONS-200", "resource_id": "u9qm9y", "response_parameters": {"method.response.header.Access-Control-Allow-Credentials": "'true'", "method.response.header.Access-Control-Allow-Headers": "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token'", "method.response.header.Access-Control-Allow-Methods": "'OPTIONS,POST,GET'", "method.response.header.Access-Control-Allow-Origin": "'https://test.thealpinestudio.com'"}, "response_templates": {}, "rest_api_id": "u3vj4dkndh", "selection_pattern": "", "status_code": "200"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["module.api_gateway.aws_api_gateway_integration.cors_integration", "module.api_gateway.aws_api_gateway_method.cors_method", "module.api_gateway.aws_api_gateway_method_response.cors_response", "module.api_gateway.aws_api_gateway_resource.rest_api_resource", "module.api_gateway.aws_api_gateway_rest_api.rest_api"]}]}, {"module": "module.api_gateway", "mode": "managed", "type": "aws_api_gateway_integration_response", "name": "rest_api_get_method_integration_response_200", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"content_handling": "", "http_method": "GET", "id": "agir-u3vj4dkndh-u9qm9y-GET-200", "resource_id": "u9qm9y", "response_parameters": {"method.response.header.Access-Control-Allow-Credentials": "'true'", "method.response.header.Access-Control-Allow-Headers": "'Content-Type, X-Amz-Date, Authorization, X-Api-Key, X-Amz-Security-Token,X-Requested-With'", "method.response.header.Access-Control-Allow-Methods": "'OPTIONS,GET'", "method.response.header.Access-Control-Allow-Origin": "'https://test.thealpinestudio.com'"}, "response_templates": null, "rest_api_id": "u3vj4dkndh", "selection_pattern": "", "status_code": "200"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["module.api_gateway.aws_api_gateway_integration.rest_api_get_method_integration", "module.api_gateway.aws_api_gateway_method.rest_api_get_method", "module.api_gateway.aws_api_gateway_method_response.rest_api_get_method_response_200", "module.api_gateway.aws_api_gateway_resource.rest_api_resource", "module.api_gateway.aws_api_gateway_rest_api.rest_api", "module.api_gateway.aws_lambda_permission.lambda_api_gw_permissions_list_products_get", "module.lambda_function.aws_iam_role.lambda_execution_role_get", "module.lambda_function.aws_lambda_function.list_products_function", "module.lambda_function.aws_s3_object.lambda_code", "module.lambda_function.data.archive_file.lambda_code_list"]}]}, {"module": "module.api_gateway", "mode": "managed", "type": "aws_api_gateway_integration_response", "name": "rest_api_post_method_integration_response_200", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"content_handling": "", "http_method": "POST", "id": "agir-u3vj4dkndh-u9qm9y-POST-200", "resource_id": "u9qm9y", "response_parameters": {"method.response.header.Access-Control-Allow-Credentials": "'true'", "method.response.header.Access-Control-Allow-Headers": "'Content-Type, X-Amz-Date, Authorization, X-Api-Key, X-Amz-Security-Token,X-Requested-With'", "method.response.header.Access-Control-Allow-Methods": "'OPTIONS,POST'", "method.response.header.Access-Control-Allow-Origin": "'https://test.thealpinestudio.com'"}, "response_templates": null, "rest_api_id": "u3vj4dkndh", "selection_pattern": "", "status_code": "200"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["module.api_gateway.aws_api_gateway_integration.rest_api_post_method_integration", "module.api_gateway.aws_api_gateway_method.rest_api_post_method", "module.api_gateway.aws_api_gateway_method_response.rest_api_post_method_response_200", "module.api_gateway.aws_api_gateway_resource.rest_api_resource", "module.api_gateway.aws_api_gateway_rest_api.rest_api", "module.api_gateway.aws_lambda_permission.lambda_api_gw_permissions_list_products_post", "module.lambda_function.aws_iam_role.lambda_execution_role_get", "module.lambda_function.aws_lambda_function.list_products_function", "module.lambda_function.aws_s3_object.lambda_code", "module.lambda_function.data.archive_file.lambda_code_list"]}]}, {"module": "module.api_gateway", "mode": "managed", "type": "aws_api_gateway_integration_response", "name": "webhooks_post_integration_response_200", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"content_handling": "", "http_method": "POST", "id": "agir-u3vj4dkndh-2xyt9t-POST-200", "resource_id": "2xyt9t", "response_parameters": {"method.response.header.Access-Control-Allow-Credentials": "'true'", "method.response.header.Access-Control-Allow-Headers": "'Content-Type, X-Amz-Date, Authorization, X-Api-Key, X-Amz-Security-Token, X-Requested-With, Stripe-Signature'", "method.response.header.Access-Control-Allow-Methods": "'OPTIONS,POST'", "method.response.header.Access-Control-Allow-Origin": "'https://test.thealpinestudio.com'"}, "response_templates": null, "rest_api_id": "u3vj4dkndh", "selection_pattern": "", "status_code": "200"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["module.api_gateway.aws_api_gateway_integration.webhooks_products_post_integration", "module.api_gateway.aws_api_gateway_method.webhooks_products_post_method", "module.api_gateway.aws_api_gateway_method_response.webhooks_post_method_response_200", "module.api_gateway.aws_api_gateway_resource.webhooks_resource", "module.api_gateway.aws_api_gateway_rest_api.rest_api", "module.lambda_function.aws_iam_role.lambda_execution_role", "module.lambda_function.aws_lambda_function.webhooks_function", "module.lambda_function.aws_s3_object.lambda_code_webhooks", "module.lambda_function.data.archive_file.lambda_code_webhooks"]}]}, {"module": "module.api_gateway", "mode": "managed", "type": "aws_api_gateway_method", "name": "checkout_products_post_method", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"api_key_required": false, "authorization": "NONE", "authorization_scopes": [], "authorizer_id": "", "http_method": "POST", "id": "agm-u3vj4dkndh-77t199-POST", "operation_name": "", "request_models": {}, "request_parameters": {"method.request.path.proxy": false}, "request_validator_id": "", "resource_id": "77t199", "rest_api_id": "u3vj4dkndh"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["module.api_gateway.aws_api_gateway_resource.checkout_products_resource", "module.api_gateway.aws_api_gateway_rest_api.rest_api"], "create_before_destroy": true}]}, {"module": "module.api_gateway", "mode": "managed", "type": "aws_api_gateway_method", "name": "contact_form_post_method", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"api_key_required": false, "authorization": "NONE", "authorization_scopes": [], "authorizer_id": "", "http_method": "POST", "id": "agm-u3vj4dkndh-m019lh-POST", "operation_name": "", "request_models": {}, "request_parameters": {}, "request_validator_id": "", "resource_id": "m019lh", "rest_api_id": "u3vj4dkndh"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["module.api_gateway.aws_api_gateway_authorizer.api_authorizer", "module.api_gateway.aws_api_gateway_resource.contact_resource", "module.api_gateway.aws_api_gateway_rest_api.rest_api", "module.cognito.aws_cognito_user_pool.user_pool"], "create_before_destroy": true}]}, {"module": "module.api_gateway", "mode": "managed", "type": "aws_api_gateway_method", "name": "cors_method", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"api_key_required": false, "authorization": "NONE", "authorization_scopes": [], "authorizer_id": "", "http_method": "OPTIONS", "id": "agm-u3vj4dkndh-u9qm9y-OPTIONS", "operation_name": "", "request_models": {}, "request_parameters": {}, "request_validator_id": "", "resource_id": "u9qm9y", "rest_api_id": "u3vj4dkndh"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["module.api_gateway.aws_api_gateway_resource.rest_api_resource", "module.api_gateway.aws_api_gateway_rest_api.rest_api"], "create_before_destroy": true}]}, {"module": "module.api_gateway", "mode": "managed", "type": "aws_api_gateway_method", "name": "rest_api_get_method", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"api_key_required": false, "authorization": "NONE", "authorization_scopes": [], "authorizer_id": "", "http_method": "GET", "id": "agm-u3vj4dkndh-u9qm9y-GET", "operation_name": "", "request_models": {}, "request_parameters": {"method.request.path.proxy": false}, "request_validator_id": "", "resource_id": "u9qm9y", "rest_api_id": "u3vj4dkndh"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["module.api_gateway.aws_api_gateway_resource.rest_api_resource", "module.api_gateway.aws_api_gateway_rest_api.rest_api"], "create_before_destroy": true}]}, {"module": "module.api_gateway", "mode": "managed", "type": "aws_api_gateway_method", "name": "rest_api_post_method", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"api_key_required": false, "authorization": "NONE", "authorization_scopes": [], "authorizer_id": "", "http_method": "POST", "id": "agm-u3vj4dkndh-u9qm9y-POST", "operation_name": "", "request_models": {}, "request_parameters": {"method.request.path.proxy": false}, "request_validator_id": "", "resource_id": "u9qm9y", "rest_api_id": "u3vj4dkndh"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["module.api_gateway.aws_api_gateway_resource.rest_api_resource", "module.api_gateway.aws_api_gateway_rest_api.rest_api"], "create_before_destroy": true}]}, {"module": "module.api_gateway", "mode": "managed", "type": "aws_api_gateway_method", "name": "webhooks_products_post_method", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"api_key_required": false, "authorization": "NONE", "authorization_scopes": [], "authorizer_id": "", "http_method": "POST", "id": "agm-u3vj4dkndh-2xyt9t-POST", "operation_name": "", "request_models": {}, "request_parameters": {}, "request_validator_id": "", "resource_id": "2xyt9t", "rest_api_id": "u3vj4dkndh"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["module.api_gateway.aws_api_gateway_resource.webhooks_resource", "module.api_gateway.aws_api_gateway_rest_api.rest_api"], "create_before_destroy": true}]}, {"module": "module.api_gateway", "mode": "managed", "type": "aws_api_gateway_method_response", "name": "checkout_products_post_method_response_200", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"http_method": "POST", "id": "agmr-u3vj4dkndh-77t199-POST-200", "resource_id": "77t199", "response_models": {}, "response_parameters": {"method.response.header.Access-Control-Allow-Credentials": true, "method.response.header.Access-Control-Allow-Headers": true, "method.response.header.Access-Control-Allow-Methods": true, "method.response.header.Access-Control-Allow-Origin": true}, "rest_api_id": "u3vj4dkndh", "status_code": "200"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["module.api_gateway.aws_api_gateway_method.checkout_products_post_method", "module.api_gateway.aws_api_gateway_resource.checkout_products_resource", "module.api_gateway.aws_api_gateway_rest_api.rest_api"]}]}, {"module": "module.api_gateway", "mode": "managed", "type": "aws_api_gateway_method_response", "name": "contact_post_method_response_200", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"http_method": "POST", "id": "agmr-u3vj4dkndh-m019lh-POST-200", "resource_id": "m019lh", "response_models": {}, "response_parameters": {"method.response.header.Access-Control-Allow-Credentials": true, "method.response.header.Access-Control-Allow-Headers": true, "method.response.header.Access-Control-Allow-Methods": true, "method.response.header.Access-Control-Allow-Origin": true}, "rest_api_id": "u3vj4dkndh", "status_code": "200"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["module.api_gateway.aws_api_gateway_authorizer.api_authorizer", "module.api_gateway.aws_api_gateway_method.contact_form_post_method", "module.api_gateway.aws_api_gateway_resource.contact_resource", "module.api_gateway.aws_api_gateway_rest_api.rest_api", "module.cognito.aws_cognito_user_pool.user_pool"]}]}, {"module": "module.api_gateway", "mode": "managed", "type": "aws_api_gateway_method_response", "name": "cors_response", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"http_method": "OPTIONS", "id": "agmr-u3vj4dkndh-u9qm9y-OPTIONS-200", "resource_id": "u9qm9y", "response_models": {"application/json": "Empty"}, "response_parameters": {"method.response.header.Access-Control-Allow-Credentials": true, "method.response.header.Access-Control-Allow-Headers": true, "method.response.header.Access-Control-Allow-Methods": true, "method.response.header.Access-Control-Allow-Origin": true, "method.response.header.Access-Control-Expose-Headers": true}, "rest_api_id": "u3vj4dkndh", "status_code": "200"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["module.api_gateway.aws_api_gateway_integration.cors_integration", "module.api_gateway.aws_api_gateway_method.cors_method", "module.api_gateway.aws_api_gateway_resource.rest_api_resource", "module.api_gateway.aws_api_gateway_rest_api.rest_api"]}]}, {"module": "module.api_gateway", "mode": "managed", "type": "aws_api_gateway_method_response", "name": "rest_api_get_method_response_200", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"http_method": "GET", "id": "agmr-u3vj4dkndh-u9qm9y-GET-200", "resource_id": "u9qm9y", "response_models": {}, "response_parameters": {"method.response.header.Access-Control-Allow-Credentials": true, "method.response.header.Access-Control-Allow-Headers": true, "method.response.header.Access-Control-Allow-Methods": true, "method.response.header.Access-Control-Allow-Origin": true}, "rest_api_id": "u3vj4dkndh", "status_code": "200"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["module.api_gateway.aws_api_gateway_method.rest_api_get_method", "module.api_gateway.aws_api_gateway_resource.rest_api_resource", "module.api_gateway.aws_api_gateway_rest_api.rest_api"]}]}, {"module": "module.api_gateway", "mode": "managed", "type": "aws_api_gateway_method_response", "name": "rest_api_post_method_response_200", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"http_method": "POST", "id": "agmr-u3vj4dkndh-u9qm9y-POST-200", "resource_id": "u9qm9y", "response_models": {}, "response_parameters": {"method.response.header.Access-Control-Allow-Credentials": true, "method.response.header.Access-Control-Allow-Headers": true, "method.response.header.Access-Control-Allow-Methods": true, "method.response.header.Access-Control-Allow-Origin": true}, "rest_api_id": "u3vj4dkndh", "status_code": "200"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["module.api_gateway.aws_api_gateway_method.rest_api_post_method", "module.api_gateway.aws_api_gateway_resource.rest_api_resource", "module.api_gateway.aws_api_gateway_rest_api.rest_api"]}]}, {"module": "module.api_gateway", "mode": "managed", "type": "aws_api_gateway_method_response", "name": "webhooks_post_method_response_200", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"http_method": "POST", "id": "agmr-u3vj4dkndh-2xyt9t-POST-200", "resource_id": "2xyt9t", "response_models": {}, "response_parameters": {"method.response.header.Access-Control-Allow-Credentials": true, "method.response.header.Access-Control-Allow-Headers": true, "method.response.header.Access-Control-Allow-Methods": true, "method.response.header.Access-Control-Allow-Origin": true}, "rest_api_id": "u3vj4dkndh", "status_code": "200"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["module.api_gateway.aws_api_gateway_method.webhooks_products_post_method", "module.api_gateway.aws_api_gateway_resource.webhooks_resource", "module.api_gateway.aws_api_gateway_rest_api.rest_api"]}]}, {"module": "module.api_gateway", "mode": "managed", "type": "aws_api_gateway_resource", "name": "checkout_products_resource", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"id": "77t199", "parent_id": "bc6wyabsu6", "path": "/dev-checkout-products", "path_part": "dev-checkout-products", "rest_api_id": "u3vj4dkndh"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["module.api_gateway.aws_api_gateway_rest_api.rest_api"], "create_before_destroy": true}]}, {"module": "module.api_gateway", "mode": "managed", "type": "aws_api_gateway_resource", "name": "contact_resource", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"id": "m019lh", "parent_id": "bc6wyabsu6", "path": "/dev-contact-email", "path_part": "dev-contact-email", "rest_api_id": "u3vj4dkndh"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["module.api_gateway.aws_api_gateway_rest_api.rest_api"], "create_before_destroy": true}]}, {"module": "module.api_gateway", "mode": "managed", "type": "aws_api_gateway_resource", "name": "rest_api_resource", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"id": "u9qm9y", "parent_id": "bc6wyabsu6", "path": "/dev-list-products", "path_part": "dev-list-products", "rest_api_id": "u3vj4dkndh"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["module.api_gateway.aws_api_gateway_rest_api.rest_api"], "create_before_destroy": true}]}, {"module": "module.api_gateway", "mode": "managed", "type": "aws_api_gateway_resource", "name": "webhooks_resource", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"id": "2xyt9t", "parent_id": "bc6wyabsu6", "path": "/dev-webhooks", "path_part": "dev-webhooks", "rest_api_id": "u3vj4dkndh"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["module.api_gateway.aws_api_gateway_rest_api.rest_api"], "create_before_destroy": true}]}, {"module": "module.api_gateway", "mode": "managed", "type": "aws_api_gateway_rest_api", "name": "rest_api", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"api_key_source": "HEADER", "arn": "arn:aws:apigateway:us-west-2::/restapis/u3vj4dkndh", "binary_media_types": [], "body": null, "created_date": "2025-05-22T16:41:37Z", "description": "", "disable_execute_api_endpoint": false, "endpoint_configuration": [{"types": ["REGIONAL"], "vpc_endpoint_ids": []}], "execution_arn": "arn:aws:execute-api:us-west-2:************:u3vj4dkndh", "fail_on_warnings": null, "id": "u3vj4dkndh", "minimum_compression_size": "", "name": "dev-lambda-api-gateway", "parameters": null, "policy": "", "put_rest_api_mode": null, "root_resource_id": "bc6wyabsu6", "tags": {}, "tags_all": {}}, "sensitive_attributes": [], "private": "bnVsbA==", "create_before_destroy": true}]}, {"module": "module.api_gateway", "mode": "managed", "type": "aws_api_gateway_stage", "name": "dev_stage", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"access_log_settings": [], "arn": "arn:aws:apigateway:us-west-2::/restapis/u3vj4dkndh/stages/test", "cache_cluster_enabled": false, "cache_cluster_size": "", "canary_settings": [], "client_certificate_id": "", "deployment_id": "j2b7gu", "description": "", "documentation_version": "", "execution_arn": "arn:aws:execute-api:us-west-2:************:u3vj4dkndh/test", "id": "ags-u3vj4dkndh-test", "invoke_url": "https://u3vj4dkndh.execute-api.us-west-2.amazonaws.com/test", "rest_api_id": "u3vj4dkndh", "stage_name": "test", "tags": null, "tags_all": {}, "variables": {"deployed_at": "2025-05-22T17:06:55Z"}, "web_acl_arn": "", "xray_tracing_enabled": false}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["module.api_gateway.aws_api_gateway_account.rest_api_account", "module.api_gateway.aws_api_gateway_authorizer.api_authorizer", "module.api_gateway.aws_api_gateway_deployment.rest_api_deployment", "module.api_gateway.aws_api_gateway_integration.checkout_products_post_integration", "module.api_gateway.aws_api_gateway_integration.contact_form_post_integration", "module.api_gateway.aws_api_gateway_integration.cors_integration", "module.api_gateway.aws_api_gateway_integration.rest_api_get_method_integration", "module.api_gateway.aws_api_gateway_integration.rest_api_post_method_integration", "module.api_gateway.aws_api_gateway_integration.webhooks_products_post_integration", "module.api_gateway.aws_api_gateway_method.checkout_products_post_method", "module.api_gateway.aws_api_gateway_method.contact_form_post_method", "module.api_gateway.aws_api_gateway_method.cors_method", "module.api_gateway.aws_api_gateway_method.rest_api_get_method", "module.api_gateway.aws_api_gateway_method.rest_api_post_method", "module.api_gateway.aws_api_gateway_method.webhooks_products_post_method", "module.api_gateway.aws_api_gateway_resource.checkout_products_resource", "module.api_gateway.aws_api_gateway_resource.contact_resource", "module.api_gateway.aws_api_gateway_resource.rest_api_resource", "module.api_gateway.aws_api_gateway_resource.webhooks_resource", "module.api_gateway.aws_api_gateway_rest_api.rest_api", "module.api_gateway.aws_iam_role.api_gateway_cloudwatch_logs", "module.api_gateway.aws_lambda_permission.lambda_api_gw_permissions_checkout", "module.api_gateway.aws_lambda_permission.lambda_api_gw_permissions_list_products_get", "module.api_gateway.aws_lambda_permission.lambda_api_gw_permissions_list_products_post", "module.cognito.aws_cognito_user_pool.user_pool", "module.lambda_function.aws_iam_role.lambda_execution_role", "module.lambda_function.aws_iam_role.lambda_execution_role_get", "module.lambda_function.aws_lambda_function.checkout_products_function", "module.lambda_function.aws_lambda_function.contact_email_function", "module.lambda_function.aws_lambda_function.list_products_function", "module.lambda_function.aws_lambda_function.webhooks_function", "module.lambda_function.aws_s3_object.lambda_code", "module.lambda_function.aws_s3_object.lambda_code_checkout", "module.lambda_function.aws_s3_object.lambda_code_contact", "module.lambda_function.aws_s3_object.lambda_code_webhooks", "module.lambda_function.data.archive_file.lambda_code_checkout", "module.lambda_function.data.archive_file.lambda_code_contact", "module.lambda_function.data.archive_file.lambda_code_list", "module.lambda_function.data.archive_file.lambda_code_webhooks"]}]}, {"module": "module.api_gateway", "mode": "managed", "type": "aws_cloudwatch_log_group", "name": "cloudwatch_api", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:logs:us-west-2:************:log-group:dev-cloudwatch_api", "id": "dev-cloudwatch_api", "kms_key_id": "", "log_group_class": "STANDARD", "name": "dev-cloudwatch_api", "name_prefix": "", "retention_in_days": 0, "skip_destroy": false, "tags": {}, "tags_all": {}}, "sensitive_attributes": [], "private": "bnVsbA=="}]}, {"module": "module.api_gateway", "mode": "managed", "type": "aws_iam_role", "name": "api_gateway_cloudwatch_logs", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:iam::************:role/api_gateway_cloudwatch_logs-dev", "assume_role_policy": "{\"Statement\":[{\"Action\":\"sts:AssumeR<PERSON>\",\"Effect\":\"Allow\",\"Principal\":{\"Service\":\"apigateway.amazonaws.com\"}}],\"Version\":\"2012-10-17\"}", "create_date": "2025-05-22T16:41:36Z", "description": "", "force_detach_policies": false, "id": "api_gateway_cloudwatch_logs-dev", "inline_policy": [{"name": "api_gateway_cloudwatch_logs", "policy": "{\"Version\":\"2012-10-17\",\"Statement\":[{\"Action\":[\"logs:CreateLogGroup\",\"logs:CreateLogStream\",\"logs:DescribeLogGroups\",\"logs:DescribeLogStreams\",\"logs:PutLogEvents\",\"logs:GetLogEvents\",\"logs:FilterLogEvents\"],\"Effect\":\"Allow\",\"Resource\":\"*\"}]}"}], "managed_policy_arns": [], "max_session_duration": 3600, "name": "api_gateway_cloudwatch_logs-dev", "name_prefix": "", "path": "/", "permissions_boundary": "", "tags": {}, "tags_all": {}, "unique_id": "AROAV7EONF4BWPYPL4CLC"}, "sensitive_attributes": [], "private": "bnVsbA=="}]}, {"module": "module.api_gateway", "mode": "managed", "type": "aws_iam_role_policy", "name": "api_gateway_cloudwatch_logs", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"id": "api_gateway_cloudwatch_logs-dev:api_gateway_cloudwatch_logs", "name": "api_gateway_cloudwatch_logs", "name_prefix": "", "policy": "{\"Version\":\"2012-10-17\",\"Statement\":[{\"Action\":[\"logs:CreateLogGroup\",\"logs:CreateLogStream\",\"logs:DescribeLogGroups\",\"logs:DescribeLogStreams\",\"logs:PutLogEvents\",\"logs:GetLogEvents\",\"logs:FilterLogEvents\"],\"Effect\":\"Allow\",\"Resource\":\"*\"}]}", "role": "api_gateway_cloudwatch_logs-dev"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["module.api_gateway.aws_iam_role.api_gateway_cloudwatch_logs"]}]}, {"module": "module.api_gateway", "mode": "managed", "type": "aws_lambda_permission", "name": "lambda_api_gw_permissions_checkout", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"action": "lambda:InvokeFunction", "event_source_token": null, "function_name": "dev-checkout-products", "function_url_auth_type": null, "id": "AllowExecutionFromAPIGateway-dev-checkout-products2", "principal": "apigateway.amazonaws.com", "principal_org_id": null, "qualifier": "", "source_account": null, "source_arn": "arn:aws:execute-api:us-west-2:************:u3vj4dkndh/*/POST/dev-checkout-products", "statement_id": "AllowExecutionFromAPIGateway-dev-checkout-products2", "statement_id_prefix": ""}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["module.api_gateway.aws_api_gateway_rest_api.rest_api"], "create_before_destroy": true}]}, {"module": "module.api_gateway", "mode": "managed", "type": "aws_lambda_permission", "name": "lambda_api_gw_permissions_contact", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"action": "lambda:InvokeFunction", "event_source_token": null, "function_name": "dev-contact-email", "function_url_auth_type": null, "id": "AllowExecutionFromAPIGateway-dev-contact-email2", "principal": "apigateway.amazonaws.com", "principal_org_id": null, "qualifier": "", "source_account": null, "source_arn": "arn:aws:execute-api:us-west-2:************:u3vj4dkndh/*/POST/dev-contact-email", "statement_id": "AllowExecutionFromAPIGateway-dev-contact-email2", "statement_id_prefix": ""}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["module.api_gateway.aws_api_gateway_authorizer.api_authorizer", "module.api_gateway.aws_api_gateway_method.contact_form_post_method", "module.api_gateway.aws_api_gateway_resource.contact_resource", "module.api_gateway.aws_api_gateway_rest_api.rest_api", "module.api_gateway.data.aws_caller_identity.current", "module.api_gateway.data.aws_region.current", "module.cognito.aws_cognito_user_pool.user_pool"]}]}, {"module": "module.api_gateway", "mode": "managed", "type": "aws_lambda_permission", "name": "lambda_api_gw_permissions_list_products_get", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"action": "lambda:InvokeFunction", "event_source_token": null, "function_name": "dev-list-products", "function_url_auth_type": null, "id": "AllowExecutionFromAPIGateway-dev-list-products2", "principal": "apigateway.amazonaws.com", "principal_org_id": null, "qualifier": "", "source_account": null, "source_arn": "arn:aws:execute-api:us-west-2:************:sasct8u996/*/GET/dev-list-products", "statement_id": "AllowExecutionFromAPIGateway-dev-list-products2", "statement_id_prefix": ""}, "sensitive_attributes": [], "private": "bnVsbA==", "create_before_destroy": true}]}, {"module": "module.api_gateway", "mode": "managed", "type": "aws_lambda_permission", "name": "lambda_api_gw_permissions_list_products_post", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"action": "lambda:InvokeFunction", "event_source_token": null, "function_name": "dev-list-products", "function_url_auth_type": null, "id": "AllowExecutionFromAPIGateway-dev-list-products", "principal": "apigateway.amazonaws.com", "principal_org_id": null, "qualifier": "", "source_account": null, "source_arn": "arn:aws:execute-api:us-west-2:************:sasct8u996/*/POST/dev-list-products", "statement_id": "AllowExecutionFromAPIGateway-dev-list-products", "statement_id_prefix": ""}, "sensitive_attributes": [], "private": "bnVsbA==", "create_before_destroy": true}]}, {"module": "module.api_gateway", "mode": "managed", "type": "aws_lambda_permission", "name": "webhooks_api_gw_permissions", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"action": "lambda:InvokeFunction", "event_source_token": null, "function_name": "dev-webhooks", "function_url_auth_type": null, "id": "AllowExecutionFromAPIGateway-dev-webhooks", "principal": "apigateway.amazonaws.com", "principal_org_id": null, "qualifier": "", "source_account": null, "source_arn": "arn:aws:execute-api:us-west-2:************:u3vj4dkndh/*/POST/dev-webhooks", "statement_id": "AllowExecutionFromAPIGateway-dev-webhooks", "statement_id_prefix": ""}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["module.api_gateway.aws_api_gateway_method.webhooks_products_post_method", "module.api_gateway.aws_api_gateway_resource.webhooks_resource", "module.api_gateway.aws_api_gateway_rest_api.rest_api", "module.api_gateway.data.aws_caller_identity.current", "module.api_gateway.data.aws_region.current"]}]}, {"module": "module.api_gateway", "mode": "managed", "type": "aws_route53_record", "name": "api_record", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 2, "attributes": {"alias": [{"evaluate_target_health": true, "name": "d-lxsosg4up5.execute-api.us-west-2.amazonaws.com", "zone_id": "Z2OJLYMUO9EFXC"}], "allow_overwrite": null, "cidr_routing_policy": [], "failover_routing_policy": [], "fqdn": "api.dev.thealpinestudio.com", "geolocation_routing_policy": [], "geoproximity_routing_policy": [], "health_check_id": "", "id": "Z04085477NKG6FGRMKK4_api.dev.thealpinestudio.com_A", "latency_routing_policy": [], "multivalue_answer_routing_policy": false, "name": "api.dev.thealpinestudio.com", "records": [], "set_identifier": "", "ttl": 0, "type": "A", "weighted_routing_policy": [], "zone_id": "Z04085477NKG6FGRMKK4"}, "sensitive_attributes": [], "private": "eyJzY2hlbWFfdmVyc2lvbiI6IjIifQ==", "dependencies": ["module.api_gateway.aws_acm_certificate.certificate", "module.api_gateway.aws_acm_certificate_validation.certificate_validation", "module.api_gateway.aws_api_gateway_domain_name.api_domain", "module.api_gateway.aws_route53_record.certificate_validation", "module.api_gateway.data.aws_route53_zone.zone"]}]}, {"module": "module.api_gateway", "mode": "managed", "type": "aws_route53_record", "name": "certificate_validation", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 2, "attributes": {"alias": [], "allow_overwrite": null, "cidr_routing_policy": [], "failover_routing_policy": [], "fqdn": "_268127fdfeb6b1f766c314e837ddd2bf.api.dev.thealpinestudio.com", "geolocation_routing_policy": [], "geoproximity_routing_policy": [], "health_check_id": "", "id": "Z04085477NKG6FGRMKK4__268127fdfeb6b1f766c314e837ddd2bf.api.dev.thealpinestudio.com._CNAME", "latency_routing_policy": [], "multivalue_answer_routing_policy": false, "name": "_268127fdfeb6b1f766c314e837ddd2bf.api.dev.thealpinestudio.com", "records": ["_522ec28f3aaa52e25a290fc2f7a8bbe9.xlfgrmvvlj.acm-validations.aws."], "set_identifier": "", "ttl": 60, "type": "CNAME", "weighted_routing_policy": [], "zone_id": "Z04085477NKG6FGRMKK4"}, "sensitive_attributes": [], "private": "eyJzY2hlbWFfdmVyc2lvbiI6IjIifQ==", "dependencies": ["module.api_gateway.aws_acm_certificate.certificate", "module.api_gateway.data.aws_route53_zone.zone"]}]}, {"module": "module.cognito", "mode": "data", "type": "aws_region", "name": "current", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"description": "US West (Oregon)", "endpoint": "ec2.us-west-2.amazonaws.com", "id": "us-west-2", "name": "us-west-2"}, "sensitive_attributes": []}]}, {"module": "module.cognito", "mode": "managed", "type": "aws_cognito_identity_pool", "name": "identity_pool", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"allow_classic_flow": false, "allow_unauthenticated_identities": true, "arn": "arn:aws:cognito-identity:us-west-2:************:identitypool/us-west-2:8b305250-8096-4712-9b2c-a4ccdc4b9d1d", "cognito_identity_providers": [{"client_id": "5v5dt6ikaea3i6tlct1t7i22ad", "provider_name": "cognito-idp.us-east-1.amazonaws.com/us-west-2_P8OmH8DJU", "server_side_token_check": false}], "developer_provider_name": "", "id": "us-west-2:8b305250-8096-4712-9b2c-a4ccdc4b9d1d", "identity_pool_name": "testing_identity_pool_thealpinestudio", "openid_connect_provider_arns": [], "saml_provider_arns": [], "supported_login_providers": {}, "tags": {}, "tags_all": {}}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["module.cognito.aws_cognito_user_pool.user_pool", "module.cognito.aws_cognito_user_pool_client.user_pool_client"]}]}, {"module": "module.cognito", "mode": "managed", "type": "aws_cognito_identity_pool_roles_attachment", "name": "identity_pool_roles", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"id": "us-west-2:8b305250-8096-4712-9b2c-a4ccdc4b9d1d", "identity_pool_id": "us-west-2:8b305250-8096-4712-9b2c-a4ccdc4b9d1d", "role_mapping": [], "roles": {"unauthenticated": "arn:aws:iam::************:role/testing-Cognito_DefaultUnauthenticatedRole"}}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["module.cognito.aws_cognito_identity_pool.identity_pool", "module.cognito.aws_cognito_user_pool.user_pool", "module.cognito.aws_cognito_user_pool_client.user_pool_client", "module.cognito.aws_iam_role.unauth_role"]}]}, {"module": "module.cognito", "mode": "managed", "type": "aws_cognito_user_pool", "name": "user_pool", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"account_recovery_setting": [{"recovery_mechanism": [{"name": "verified_email", "priority": 1}, {"name": "verified_phone_number", "priority": 2}]}], "admin_create_user_config": [{"allow_admin_create_user_only": false, "invite_message_template": []}], "alias_attributes": null, "arn": "arn:aws:cognito-idp:us-west-2:************:userpool/us-west-2_WD5hr07nW", "auto_verified_attributes": [], "creation_date": "2025-05-22T16:41:37Z", "custom_domain": "", "deletion_protection": "INACTIVE", "device_configuration": [], "domain": "", "email_configuration": [{"configuration_set": "", "email_sending_account": "COGNITO_DEFAULT", "from_email_address": "", "reply_to_email_address": "", "source_arn": ""}], "email_verification_message": "", "email_verification_subject": "", "endpoint": "cognito-idp.us-west-2.amazonaws.com/us-west-2_WD5hr07nW", "estimated_number_of_users": 0, "id": "us-west-2_WD5hr07nW", "lambda_config": [], "last_modified_date": "2025-05-22T16:41:37Z", "mfa_configuration": "OFF", "name": "dev-alpinestudio-user-pool", "password_policy": [{"minimum_length": 8, "require_lowercase": true, "require_numbers": true, "require_symbols": true, "require_uppercase": true, "temporary_password_validity_days": 7}], "schema": [], "sms_authentication_message": "", "sms_configuration": [], "sms_verification_message": "", "software_token_mfa_configuration": [], "tags": {}, "tags_all": {}, "user_attribute_update_settings": [], "user_pool_add_ons": [], "username_attributes": [], "username_configuration": [{"case_sensitive": true}], "verification_message_template": [{"default_email_option": "CONFIRM_WITH_CODE", "email_message": "", "email_message_by_link": "", "email_subject": "", "email_subject_by_link": "", "sms_message": ""}]}, "sensitive_attributes": [], "private": "bnVsbA==", "create_before_destroy": true}]}, {"module": "module.cognito", "mode": "managed", "type": "aws_cognito_user_pool_client", "name": "user_pool_client", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"access_token_validity": 0, "allowed_oauth_flows": [], "allowed_oauth_flows_user_pool_client": false, "allowed_oauth_scopes": [], "analytics_configuration": [], "auth_session_validity": 3, "callback_urls": [], "client_secret": "", "default_redirect_uri": "", "enable_propagate_additional_user_context_data": false, "enable_token_revocation": true, "explicit_auth_flows": ["ALLOW_REFRESH_TOKEN_AUTH", "ALLOW_USER_PASSWORD_AUTH", "ALLOW_USER_SRP_AUTH"], "generate_secret": null, "id": "5v5dt6ikaea3i6tlct1t7i22ad", "id_token_validity": 0, "logout_urls": [], "name": "testing-api-gateway-client-pool", "prevent_user_existence_errors": "ENABLED", "read_attributes": [], "refresh_token_validity": 30, "supported_identity_providers": [], "token_validity_units": [], "user_pool_id": "us-west-2_WD5hr07nW", "write_attributes": []}, "sensitive_attributes": [[{"type": "get_attr", "value": "client_secret"}]], "dependencies": ["module.cognito.aws_cognito_user_pool.user_pool"]}]}, {"module": "module.cognito", "mode": "managed", "type": "aws_iam_role", "name": "unauth_role", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:iam::************:role/testing-Cognito_DefaultUnauthenticatedRole", "assume_role_policy": "{\"Statement\":[{\"Action\":\"sts:AssumeRoleWithWebIdentity\",\"Condition\":{\"ForAnyValue:StringLike\":{\"cognito-identity.amazonaws.com:amr\":\"unauthenticated\"},\"StringEquals\":{\"cognito-identity.amazonaws.com:aud\":\"us-west-2:8b305250-8096-4712-9b2c-a4ccdc4b9d1d\"}},\"Effect\":\"Allow\",\"Principal\":{\"Federated\":\"cognito-identity.amazonaws.com\"}}],\"Version\":\"2012-10-17\"}", "create_date": "2025-05-22T16:41:39Z", "description": "", "force_detach_policies": false, "id": "testing-Cognito_DefaultUnauthenticatedRole", "inline_policy": [{"name": "testing-APIGatewayAccessPolicy", "policy": "{\"Version\":\"2012-10-17\",\"Statement\":[{\"Action\":[\"execute-api:Invoke\"],\"Effect\":\"Allow\",\"Resource\":\"arn:aws:execute-api:us-west-2:************:************/*\"}]}"}], "managed_policy_arns": [], "max_session_duration": 3600, "name": "testing-Cognito_DefaultUnauthenticatedRole", "name_prefix": "", "path": "/", "permissions_boundary": "", "tags": {}, "tags_all": {}, "unique_id": "AROAV7EONF4BWQGMXZ77C"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["module.cognito.aws_cognito_identity_pool.identity_pool", "module.cognito.aws_cognito_user_pool.user_pool", "module.cognito.aws_cognito_user_pool_client.user_pool_client"]}]}, {"module": "module.cognito", "mode": "managed", "type": "aws_iam_role_policy", "name": "api_gateway_access", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"id": "testing-Cognito_DefaultUnauthenticatedRole:testing-APIGatewayAccessPolicy", "name": "testing-APIGatewayAccessPolicy", "name_prefix": "", "policy": "{\"Version\":\"2012-10-17\",\"Statement\":[{\"Action\":[\"execute-api:Invoke\"],\"Effect\":\"Allow\",\"Resource\":\"arn:aws:execute-api:us-west-2:************:************/*\"}]}", "role": "testing-Cognito_DefaultUnauthenticatedRole"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["module.cognito.aws_cognito_identity_pool.identity_pool", "module.cognito.aws_cognito_user_pool.user_pool", "module.cognito.aws_cognito_user_pool_client.user_pool_client", "module.cognito.aws_iam_role.unauth_role"]}]}, {"module": "module.lambda_function", "mode": "data", "type": "archive_file", "name": "lambda_code_checkout", "provider": "provider[\"registry.terraform.io/hashicorp/archive\"]", "instances": [{"schema_version": 0, "attributes": {"excludes": null, "id": "88700579c95e09293adf210ced37540cf79abb4e", "output_base64sha256": "hO13lFDav8s22vHRICM2D5GRzTtKq1V4l/VGiXtoW0w=", "output_file_mode": null, "output_md5": "5b567d51091f2bdc1066b1f17ebed388", "output_path": "./dev-checkout-products.zip", "output_sha": "88700579c95e09293adf210ced37540cf79abb4e", "output_size": 8298242, "source": [], "source_content": null, "source_content_filename": null, "source_dir": "../../../lambda/dev/CheckoutProducts", "source_file": null, "type": "zip"}, "sensitive_attributes": []}]}, {"module": "module.lambda_function", "mode": "data", "type": "archive_file", "name": "lambda_code_contact", "provider": "provider[\"registry.terraform.io/hashicorp/archive\"]", "instances": [{"schema_version": 0, "attributes": {"excludes": null, "id": "e5a0cbd65fdd24b350314775cd12a5aeca511490", "output_base64sha256": "OMcfOT7Zw9e10bA1TAp2wTN+wnox38SiNLIjALJgt0Q=", "output_file_mode": null, "output_md5": "649eba37d984ac12716815dab584829b", "output_path": "./dev-contact-email.zip", "output_sha": "e5a0cbd65fdd24b350314775cd12a5aeca511490", "output_size": 10676181, "source": [], "source_content": null, "source_content_filename": null, "source_dir": "../../../lambda/dev/ContactEmail", "source_file": null, "type": "zip"}, "sensitive_attributes": []}]}, {"module": "module.lambda_function", "mode": "data", "type": "archive_file", "name": "lambda_code_list", "provider": "provider[\"registry.terraform.io/hashicorp/archive\"]", "instances": [{"schema_version": 0, "attributes": {"excludes": null, "id": "e53191bf5d5d9f116c1f94c66e56f4e01ae63a4e", "output_base64sha256": "hV7QzRPKvpBHQP7Y5KSaFHtkReiypgGPlbZXBZifN1A=", "output_file_mode": null, "output_md5": "1df85f6208668d36c5766cc50a07173a", "output_path": "./dev-list-products.zip", "output_sha": "e53191bf5d5d9f116c1f94c66e56f4e01ae63a4e", "output_size": 15732049, "source": [], "source_content": null, "source_content_filename": null, "source_dir": "../../../lambda/dev/ListProducts", "source_file": null, "type": "zip"}, "sensitive_attributes": []}]}, {"module": "module.lambda_function", "mode": "data", "type": "archive_file", "name": "lambda_code_webhooks", "provider": "provider[\"registry.terraform.io/hashicorp/archive\"]", "instances": [{"schema_version": 0, "attributes": {"excludes": null, "id": "d06b43784585dc5e69066c3fd7213ab98a7af484", "output_base64sha256": "oQ8p2ObBemFqSSKVwLm/vcltZ+LoY6bNGJXV/XSEwBM=", "output_file_mode": null, "output_md5": "71eed02d025014dc5861b262eeab5536", "output_path": "./dev-webhooks.zip", "output_sha": "d06b43784585dc5e69066c3fd7213ab98a7af484", "output_size": 10972882, "source": [], "source_content": null, "source_content_filename": null, "source_dir": "../../../lambda/dev/Webhooks", "source_file": null, "type": "zip"}, "sensitive_attributes": []}]}, {"module": "module.lambda_function", "mode": "managed", "type": "aws_iam_role", "name": "lambda_execution_role", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:iam::************:role/lambda_execution_role_dev-list-products", "assume_role_policy": "{\"Statement\":[{\"Action\":\"sts:AssumeRole\",\"Effect\":\"Allow\",\"Principal\":{\"Service\":\"lambda.amazonaws.com\"},\"<PERSON>\":\"\"}],\"Version\":\"2012-10-17\"}", "create_date": "2025-05-22T16:41:36Z", "description": "", "force_detach_policies": false, "id": "lambda_execution_role_dev-list-products", "inline_policy": [{"name": "dev_lambda_execution_policy_checkout_products_function", "policy": "{\"Version\":\"2012-10-17\",\"Statement\":[{\"Action\":[\"logs:CreateLogGroup\",\"logs:CreateLogStream\",\"logs:PutLogEvents\"],\"Effect\":\"Allow\",\"Resource\":\"arn:aws:logs:*:*:*\"},{\"Action\":[\"s3:PutObject\",\"s3:GetObjectAcl\",\"s3:GetObject\",\"s3:DeleteObject\"],\"Effect\":\"Allow\",\"Resource\":[\"arn:aws:s3:::dev-thealpinestudio-lambda-functions-v1/*\",\"arn:aws:s3:::dev-thealpinestudio-lambda-functions-v1/*\"]},{\"Action\":[\"execute-api:Invoke\"],\"Effect\":\"Allow\",\"Resource\":\"arn:aws:execute-api:*:*:*\"}]}"}, {"name": "lambda_execution_policy_dev-list-products", "policy": "{\"Version\":\"2012-10-17\",\"Statement\":[{\"Action\":[\"logs:CreateLogGroup\",\"logs:CreateLogStream\",\"logs:PutLogEvents\"],\"Effect\":\"Allow\",\"Resource\":\"arn:aws:logs:*:*:*\"},{\"Action\":[\"s3:PutObject\",\"s3:GetObjectAcl\",\"s3:GetObject\",\"s3:DeleteObject\"],\"Effect\":\"Allow\",\"Resource\":[\"arn:aws:s3:::dev-thealpinestudio-lambda-functions-v1/*\",\"arn:aws:s3:::dev-thealpinestudio-lambda-functions-v1/*\"]},{\"Action\":[\"execute-api:Invoke\"],\"Effect\":\"Allow\",\"Resource\":\"arn:aws:execute-api:*:*:*\"},{\"Action\":[\"dynamodb:PutItem\",\"dynamodb:UpdateItem\",\"dynamodb:GetItem\",\"dynamodb:DescribeTable\",\"dynamodb:DeleteItem\",\"dynamodb:Query\",\"dynamodb:Scan\"],\"Effect\":\"Allow\",\"Resource\":\"arn:aws:dynamodb:us-west-2:************:table/dev-orders\"},{\"Action\":[\"lambda:InvokeFunction\"],\"Effect\":\"Allow\",\"Resource\":[\"arn:aws:lambda:us-west-2:************:function:dev-list-products\",\"arn:aws:lambda:us-west-2:************:function:dev-checkout-products\",\"arn:aws:lambda:us-west-2:************:function:dev-webhooks\",\"arn:aws:lambda:us-west-2:************:function:dev-contact-email\"]}]}"}], "managed_policy_arns": [], "max_session_duration": 3600, "name": "lambda_execution_role_dev-list-products", "name_prefix": "", "path": "/", "permissions_boundary": "", "tags": {}, "tags_all": {}, "unique_id": "AROAV7EONF4B4AEFMHN77"}, "sensitive_attributes": [], "private": "bnVsbA==", "create_before_destroy": true}]}, {"module": "module.lambda_function", "mode": "managed", "type": "aws_iam_role", "name": "lambda_execution_role_checkout", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:iam::************:role/dev-lambda_execution_role_checkout_products_function", "assume_role_policy": "{\"Statement\":[{\"Action\":\"sts:AssumeRole\",\"Effect\":\"Allow\",\"Principal\":{\"Service\":\"lambda.amazonaws.com\"},\"<PERSON>\":\"\"}],\"Version\":\"2012-10-17\"}", "create_date": "2025-05-22T16:41:37Z", "description": "", "force_detach_policies": false, "id": "dev-lambda_execution_role_checkout_products_function", "inline_policy": [], "managed_policy_arns": [], "max_session_duration": 3600, "name": "dev-lambda_execution_role_checkout_products_function", "name_prefix": "", "path": "/", "permissions_boundary": "", "tags": {}, "tags_all": {}, "unique_id": "AROAV7EONF4BWF5K7KZHR"}, "sensitive_attributes": [], "private": "bnVsbA=="}]}, {"module": "module.lambda_function", "mode": "managed", "type": "aws_iam_role", "name": "lambda_execution_role_get", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:iam::************:role/lambda_execution_role_dev_get_products_function", "assume_role_policy": "{\"Statement\":[{\"Action\":\"sts:AssumeRole\",\"Effect\":\"Allow\",\"Principal\":{\"Service\":\"lambda.amazonaws.com\"},\"<PERSON>\":\"\"}],\"Version\":\"2012-10-17\"}", "create_date": "2025-05-22T16:41:36Z", "description": "", "force_detach_policies": false, "id": "lambda_execution_role_dev_get_products_function", "inline_policy": [{"name": "lambda_execution_policy_dev_list_products_function", "policy": "{\"Version\":\"2012-10-17\",\"Statement\":[{\"Action\":[\"logs:CreateLogGroup\",\"logs:CreateLogStream\",\"logs:PutLogEvents\"],\"Effect\":\"Allow\",\"Resource\":\"arn:aws:logs:*:*:*\"},{\"Action\":[\"dynamodb:Query\",\"dynamodb:Scan\",\"dynamodb:GetItem\",\"dynamodb:DescribeTable\"],\"Effect\":\"Allow\",\"Resource\":[\"arn:aws:dynamodb:us-west-2:************:table/products\",\"arn:aws:s3:::admin-thealpinestudio-images\",\"arn:aws:dynamodb:us-west-2:************:table/products/index/*\"]}]}"}], "managed_policy_arns": [], "max_session_duration": 3600, "name": "lambda_execution_role_dev_get_products_function", "name_prefix": "", "path": "/", "permissions_boundary": "", "tags": {}, "tags_all": {}, "unique_id": "AROAV7EONF4B5RPTZN6T5"}, "sensitive_attributes": [], "private": "bnVsbA==", "create_before_destroy": true}]}, {"module": "module.lambda_function", "mode": "managed", "type": "aws_iam_role_policy", "name": "lambda_execution_policy", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"id": "lambda_execution_role_dev-list-products:lambda_execution_policy_dev-list-products", "name": "lambda_execution_policy_dev-list-products", "name_prefix": "", "policy": "{\"Version\":\"2012-10-17\",\"Statement\":[{\"Action\":[\"logs:CreateLogGroup\",\"logs:CreateLogStream\",\"logs:PutLogEvents\"],\"Effect\":\"Allow\",\"Resource\":\"arn:aws:logs:*:*:*\"},{\"Action\":[\"s3:PutObject\",\"s3:GetObjectAcl\",\"s3:GetObject\",\"s3:DeleteObject\"],\"Effect\":\"Allow\",\"Resource\":[\"arn:aws:s3:::dev-thealpinestudio-lambda-functions-v1/*\",\"arn:aws:s3:::dev-thealpinestudio-lambda-functions-v1/*\"]},{\"Action\":[\"execute-api:Invoke\"],\"Effect\":\"Allow\",\"Resource\":\"arn:aws:execute-api:*:*:*\"},{\"Action\":[\"dynamodb:PutItem\",\"dynamodb:UpdateItem\",\"dynamodb:GetItem\",\"dynamodb:DescribeTable\",\"dynamodb:DeleteItem\",\"dynamodb:Query\",\"dynamodb:Scan\"],\"Effect\":\"Allow\",\"Resource\":\"arn:aws:dynamodb:us-west-2:************:table/dev-orders\"},{\"Action\":[\"lambda:InvokeFunction\"],\"Effect\":\"Allow\",\"Resource\":[\"arn:aws:lambda:us-west-2:************:function:dev-list-products\",\"arn:aws:lambda:us-west-2:************:function:dev-checkout-products\",\"arn:aws:lambda:us-west-2:************:function:dev-webhooks\",\"arn:aws:lambda:us-west-2:************:function:dev-contact-email\"]}]}", "role": "lambda_execution_role_dev-list-products"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["module.lambda_function.aws_iam_role.lambda_execution_role"]}]}, {"module": "module.lambda_function", "mode": "managed", "type": "aws_iam_role_policy", "name": "lambda_execution_policy_checkout", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"id": "lambda_execution_role_dev-list-products:dev_lambda_execution_policy_checkout_products_function", "name": "dev_lambda_execution_policy_checkout_products_function", "name_prefix": "", "policy": "{\"Version\":\"2012-10-17\",\"Statement\":[{\"Action\":[\"logs:CreateLogGroup\",\"logs:CreateLogStream\",\"logs:PutLogEvents\"],\"Effect\":\"Allow\",\"Resource\":\"arn:aws:logs:*:*:*\"},{\"Action\":[\"s3:PutObject\",\"s3:GetObjectAcl\",\"s3:GetObject\",\"s3:DeleteObject\"],\"Effect\":\"Allow\",\"Resource\":[\"arn:aws:s3:::dev-thealpinestudio-lambda-functions-v1/*\",\"arn:aws:s3:::dev-thealpinestudio-lambda-functions-v1/*\"]},{\"Action\":[\"execute-api:Invoke\"],\"Effect\":\"Allow\",\"Resource\":\"arn:aws:execute-api:*:*:*\"}]}", "role": "lambda_execution_role_dev-list-products"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["module.lambda_function.aws_iam_role.lambda_execution_role"]}]}, {"module": "module.lambda_function", "mode": "managed", "type": "aws_iam_role_policy", "name": "lambda_execution_policy_get", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"id": "lambda_execution_role_dev_get_products_function:lambda_execution_policy_dev_list_products_function", "name": "lambda_execution_policy_dev_list_products_function", "name_prefix": "", "policy": "{\"Version\":\"2012-10-17\",\"Statement\":[{\"Action\":[\"logs:CreateLogGroup\",\"logs:CreateLogStream\",\"logs:PutLogEvents\"],\"Effect\":\"Allow\",\"Resource\":\"arn:aws:logs:*:*:*\"},{\"Action\":[\"dynamodb:Query\",\"dynamodb:Scan\",\"dynamodb:GetItem\",\"dynamodb:DescribeTable\"],\"Effect\":\"Allow\",\"Resource\":[\"arn:aws:dynamodb:us-west-2:************:table/products\",\"arn:aws:s3:::admin-thealpinestudio-images\",\"arn:aws:dynamodb:us-west-2:************:table/products/index/*\"]}]}", "role": "lambda_execution_role_dev_get_products_function"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["module.lambda_function.aws_iam_role.lambda_execution_role_get"]}]}, {"module": "module.lambda_function", "mode": "managed", "type": "aws_lambda_function", "name": "checkout_products_function", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"architectures": ["x86_64"], "arn": "arn:aws:lambda:us-west-2:************:function:dev-checkout-products", "code_signing_config_arn": "", "dead_letter_config": [], "description": "", "environment": [{"variables": {"BUCKET_NAME": "dev-thealpinestudio-lambda-functions-v1", "DYNAMODB_ORDERS_TABLE": "dev-orders", "FRONTEND_URL": "https://test.thealpinestudio.com", "STRIPE_SECRET_KEY": "sk_test_51NirT5H1KC3YYzQbwoT6Tewe6rFzhf3e5xmWrgIw75V01q126czeifShPGVOSf00K8Pblw7FUA5SaBEXfW5VnXb700uS8rSOyL", "s3_bucket_NAME": "dev-thealpinestudio-lambda-functions-v1"}}], "ephemeral_storage": [{"size": 512}], "file_system_config": [], "filename": null, "function_name": "dev-checkout-products", "handler": "dev-checkout-products", "id": "dev-checkout-products", "image_config": [], "image_uri": "", "invoke_arn": "arn:aws:apigateway:us-west-2:lambda:path/2015-03-31/functions/arn:aws:lambda:us-west-2:************:function:dev-checkout-products/invocations", "kms_key_arn": "", "last_modified": "2025-05-22T17:06:46.715+0000", "layers": null, "logging_config": [{"application_log_level": "", "log_format": "Text", "log_group": "/aws/lambda/dev-checkout-products", "system_log_level": ""}], "memory_size": 128, "package_type": "Zip", "publish": false, "qualified_arn": "arn:aws:lambda:us-west-2:************:function:dev-checkout-products:$LATEST", "qualified_invoke_arn": "arn:aws:apigateway:us-west-2:lambda:path/2015-03-31/functions/arn:aws:lambda:us-west-2:************:function:dev-checkout-products:$LATEST/invocations", "replace_security_groups_on_destroy": null, "replacement_security_group_ids": null, "reserved_concurrent_executions": -1, "role": "arn:aws:iam::************:role/lambda_execution_role_dev-list-products", "runtime": "provided.al2", "s3_bucket": "dev-thealpinestudio-lambda-functions-v1", "s3_key": "dev-checkout-products.zip", "s3_object_version": null, "signing_job_arn": "", "signing_profile_version_arn": "", "skip_destroy": false, "snap_start": [], "source_code_hash": "hO13lFDav8s22vHRICM2D5GRzTtKq1V4l/VGiXtoW0w=", "source_code_size": 8298242, "tags": null, "tags_all": {}, "timeout": 3, "timeouts": null, "tracing_config": [{"mode": "PassThrough"}], "version": "$LATEST", "vpc_config": []}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjo2MDAwMDAwMDAwMDAsImRlbGV0ZSI6NjAwMDAwMDAwMDAwLCJ1cGRhdGUiOjYwMDAwMDAwMDAwMH19", "dependencies": ["module.lambda_function.aws_iam_role.lambda_execution_role", "module.lambda_function.aws_s3_object.lambda_code_checkout", "module.lambda_function.data.archive_file.lambda_code_checkout"], "create_before_destroy": true}]}, {"module": "module.lambda_function", "mode": "managed", "type": "aws_lambda_function", "name": "contact_email_function", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"architectures": ["x86_64"], "arn": "arn:aws:lambda:us-west-2:************:function:dev-contact-email", "code_signing_config_arn": "", "dead_letter_config": [], "description": "", "environment": [{"variables": {"BUCKET_NAME": "dev-thealpinestudio-lambda-functions-v1", "STRIPE_SECRET_KEY": "sk_test_51NirT5H1KC3YYzQbwoT6Tewe6rFzhf3e5xmWrgIw75V01q126czeifShPGVOSf00K8Pblw7FUA5SaBEXfW5VnXb700uS8rSOyL", "s3_bucket_NAME": "dev-thealpinestudio-lambda-functions-v1"}}], "ephemeral_storage": [{"size": 512}], "file_system_config": [], "filename": null, "function_name": "dev-contact-email", "handler": "dev-contact-email", "id": "dev-contact-email", "image_config": [], "image_uri": "", "invoke_arn": "arn:aws:apigateway:us-west-2:lambda:path/2015-03-31/functions/arn:aws:lambda:us-west-2:************:function:dev-contact-email/invocations", "kms_key_arn": "", "last_modified": "2025-05-22T17:06:49.588+0000", "layers": null, "logging_config": [{"application_log_level": "", "log_format": "Text", "log_group": "/aws/lambda/dev-contact-email", "system_log_level": ""}], "memory_size": 128, "package_type": "Zip", "publish": false, "qualified_arn": "arn:aws:lambda:us-west-2:************:function:dev-contact-email:$LATEST", "qualified_invoke_arn": "arn:aws:apigateway:us-west-2:lambda:path/2015-03-31/functions/arn:aws:lambda:us-west-2:************:function:dev-contact-email:$LATEST/invocations", "replace_security_groups_on_destroy": null, "replacement_security_group_ids": null, "reserved_concurrent_executions": -1, "role": "arn:aws:iam::************:role/lambda_execution_role_dev-list-products", "runtime": "provided.al2", "s3_bucket": "dev-thealpinestudio-lambda-functions-v1", "s3_key": "contact-email.zip", "s3_object_version": null, "signing_job_arn": "", "signing_profile_version_arn": "", "skip_destroy": false, "snap_start": [], "source_code_hash": "OMcfOT7Zw9e10bA1TAp2wTN+wnox38SiNLIjALJgt0Q=", "source_code_size": 10676181, "tags": null, "tags_all": {}, "timeout": 3, "timeouts": null, "tracing_config": [{"mode": "PassThrough"}], "version": "$LATEST", "vpc_config": []}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjo2MDAwMDAwMDAwMDAsImRlbGV0ZSI6NjAwMDAwMDAwMDAwLCJ1cGRhdGUiOjYwMDAwMDAwMDAwMH19", "dependencies": ["module.lambda_function.aws_iam_role.lambda_execution_role", "module.lambda_function.aws_s3_object.lambda_code_contact", "module.lambda_function.data.archive_file.lambda_code_contact"], "create_before_destroy": true}]}, {"module": "module.lambda_function", "mode": "managed", "type": "aws_lambda_function", "name": "list_products_function", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"architectures": ["x86_64"], "arn": "arn:aws:lambda:us-west-2:************:function:dev-list-products", "code_signing_config_arn": "", "dead_letter_config": [], "description": "", "environment": [{"variables": {"BUCKET_NAME": "dev-thealpinestudio-lambda-functions-v1", "TABLE_NAME": "products", "s3_bucket_NAME": "dev-thealpinestudio-lambda-functions-v1"}}], "ephemeral_storage": [{"size": 512}], "file_system_config": [], "filename": null, "function_name": "dev-list-products", "handler": "dev-list-products", "id": "dev-list-products", "image_config": [], "image_uri": "", "invoke_arn": "arn:aws:apigateway:us-west-2:lambda:path/2015-03-31/functions/arn:aws:lambda:us-west-2:************:function:dev-list-products/invocations", "kms_key_arn": "", "last_modified": "2025-05-22T17:06:48.546+0000", "layers": null, "logging_config": [{"application_log_level": "", "log_format": "Text", "log_group": "/aws/lambda/dev-list-products", "system_log_level": ""}], "memory_size": 128, "package_type": "Zip", "publish": false, "qualified_arn": "arn:aws:lambda:us-west-2:************:function:dev-list-products:$LATEST", "qualified_invoke_arn": "arn:aws:apigateway:us-west-2:lambda:path/2015-03-31/functions/arn:aws:lambda:us-west-2:************:function:dev-list-products:$LATEST/invocations", "replace_security_groups_on_destroy": null, "replacement_security_group_ids": null, "reserved_concurrent_executions": -1, "role": "arn:aws:iam::************:role/lambda_execution_role_dev_get_products_function", "runtime": "provided.al2", "s3_bucket": "dev-thealpinestudio-lambda-functions-v1", "s3_key": "list-products.zip", "s3_object_version": null, "signing_job_arn": "", "signing_profile_version_arn": "", "skip_destroy": false, "snap_start": [], "source_code_hash": "hV7QzRPKvpBHQP7Y5KSaFHtkReiypgGPlbZXBZifN1A=", "source_code_size": 15732049, "tags": null, "tags_all": {}, "timeout": 3, "timeouts": null, "tracing_config": [{"mode": "PassThrough"}], "version": "$LATEST", "vpc_config": []}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjo2MDAwMDAwMDAwMDAsImRlbGV0ZSI6NjAwMDAwMDAwMDAwLCJ1cGRhdGUiOjYwMDAwMDAwMDAwMH19", "dependencies": ["module.lambda_function.aws_iam_role.lambda_execution_role_get", "module.lambda_function.aws_s3_object.lambda_code", "module.lambda_function.data.archive_file.lambda_code_list"], "create_before_destroy": true}]}, {"module": "module.lambda_function", "mode": "managed", "type": "aws_lambda_function", "name": "webhooks_function", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"architectures": ["x86_64"], "arn": "arn:aws:lambda:us-west-2:************:function:dev-webhooks", "code_signing_config_arn": "", "dead_letter_config": [], "description": "", "environment": [{"variables": {"BUCKET_NAME": "dev-thealpinestudio-lambda-functions-v1", "RESEND_API_KEY": "re_ad1veToT_2yFABNEpT9F3dqUMy73Q6P3t", "STRIPE_ENDPOINT_SECRET": "whsec_Kh2KKZZnTceUvS6YCngbv75A2YatEgzy", "STRIPE_SECRET_KEY": "sk_test_51NirT5H1KC3YYzQbwoT6Tewe6rFzhf3e5xmWrgIw75V01q126czeifShPGVOSf00K8Pblw7FUA5SaBEXfW5VnXb700uS8rSOyL", "s3_bucket_NAME": "dev-thealpinestudio-lambda-functions-v1"}}], "ephemeral_storage": [{"size": 512}], "file_system_config": [], "filename": null, "function_name": "dev-webhooks", "handler": "dev-webhooks", "id": "dev-webhooks", "image_config": [], "image_uri": "", "invoke_arn": "arn:aws:apigateway:us-west-2:lambda:path/2015-03-31/functions/arn:aws:lambda:us-west-2:************:function:dev-webhooks/invocations", "kms_key_arn": "", "last_modified": "2025-05-22T17:06:46.532+0000", "layers": null, "logging_config": [{"application_log_level": "", "log_format": "Text", "log_group": "/aws/lambda/dev-webhooks", "system_log_level": ""}], "memory_size": 128, "package_type": "Zip", "publish": false, "qualified_arn": "arn:aws:lambda:us-west-2:************:function:dev-webhooks:$LATEST", "qualified_invoke_arn": "arn:aws:apigateway:us-west-2:lambda:path/2015-03-31/functions/arn:aws:lambda:us-west-2:************:function:dev-webhooks:$LATEST/invocations", "replace_security_groups_on_destroy": null, "replacement_security_group_ids": null, "reserved_concurrent_executions": -1, "role": "arn:aws:iam::************:role/lambda_execution_role_dev-list-products", "runtime": "provided.al2", "s3_bucket": "dev-thealpinestudio-lambda-functions-v1", "s3_key": "webhooks.zip", "s3_object_version": null, "signing_job_arn": "", "signing_profile_version_arn": "", "skip_destroy": false, "snap_start": [], "source_code_hash": "oQ8p2ObBemFqSSKVwLm/vcltZ+LoY6bNGJXV/XSEwBM=", "source_code_size": 10972882, "tags": null, "tags_all": {}, "timeout": 3, "timeouts": null, "tracing_config": [{"mode": "PassThrough"}], "version": "$LATEST", "vpc_config": []}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjo2MDAwMDAwMDAwMDAsImRlbGV0ZSI6NjAwMDAwMDAwMDAwLCJ1cGRhdGUiOjYwMDAwMDAwMDAwMH19", "dependencies": ["module.lambda_function.aws_iam_role.lambda_execution_role", "module.lambda_function.aws_s3_object.lambda_code_webhooks", "module.lambda_function.data.archive_file.lambda_code_webhooks"], "create_before_destroy": true}]}, {"module": "module.lambda_function", "mode": "managed", "type": "aws_lambda_permission", "name": "lambda_api_gw_permissions_GET", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"action": "lambda:InvokeFunction", "event_source_token": null, "function_name": "arn:aws:lambda:us-west-2:************:function:dev-list-products", "function_url_auth_type": null, "id": "AllowExecutionFromAPIGatewayGET", "principal": "apigateway.amazonaws.com", "principal_org_id": null, "qualifier": "", "source_account": null, "source_arn": "arn:aws:execute-api:us-west-2:************:u3vj4dkndh/*/GET/dev-list-products", "statement_id": "AllowExecutionFromAPIGatewayGET", "statement_id_prefix": ""}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["module.api_gateway.aws_api_gateway_rest_api.rest_api", "module.lambda_function.aws_iam_role.lambda_execution_role_get", "module.lambda_function.aws_lambda_function.list_products_function", "module.lambda_function.aws_s3_object.lambda_code", "module.lambda_function.data.archive_file.lambda_code_list"]}]}, {"module": "module.lambda_function", "mode": "managed", "type": "aws_lambda_permission", "name": "lambda_api_gw_permissions_GET_checkout", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"action": "lambda:InvokeFunction", "event_source_token": null, "function_name": "arn:aws:lambda:us-west-2:************:function:dev-checkout-products", "function_url_auth_type": null, "id": "AllowExecutionFromAPIGatewayGET", "principal": "apigateway.amazonaws.com", "principal_org_id": null, "qualifier": "", "source_account": null, "source_arn": "arn:aws:execute-api:us-west-2:************:u3vj4dkndh/*/GET/dev-checkout-products", "statement_id": "AllowExecutionFromAPIGatewayGET", "statement_id_prefix": ""}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["module.api_gateway.aws_api_gateway_rest_api.rest_api", "module.lambda_function.aws_iam_role.lambda_execution_role", "module.lambda_function.aws_lambda_function.checkout_products_function", "module.lambda_function.aws_s3_object.lambda_code_checkout", "module.lambda_function.data.archive_file.lambda_code_checkout"]}]}, {"module": "module.lambda_function", "mode": "managed", "type": "aws_lambda_permission", "name": "lambda_api_gw_permissions_GET_contact", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"action": "lambda:InvokeFunction", "event_source_token": null, "function_name": "arn:aws:lambda:us-west-2:************:function:dev-contact-email", "function_url_auth_type": null, "id": "AllowExecutionFromAPIGatewayGET", "principal": "apigateway.amazonaws.com", "principal_org_id": null, "qualifier": "", "source_account": null, "source_arn": "arn:aws:execute-api:us-west-2:************:u3vj4dkndh/*/GET/contact-email", "statement_id": "AllowExecutionFromAPIGatewayGET", "statement_id_prefix": ""}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["module.api_gateway.aws_api_gateway_rest_api.rest_api", "module.lambda_function.aws_iam_role.lambda_execution_role", "module.lambda_function.aws_lambda_function.contact_email_function", "module.lambda_function.aws_s3_object.lambda_code_contact", "module.lambda_function.data.archive_file.lambda_code_contact"]}]}, {"module": "module.lambda_function", "mode": "managed", "type": "aws_lambda_permission", "name": "lambda_api_gw_permissions_GET_webhooks", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"action": "lambda:InvokeFunction", "event_source_token": null, "function_name": "arn:aws:lambda:us-west-2:************:function:dev-webhooks", "function_url_auth_type": null, "id": "AllowExecutionFromAPIGatewayGET", "principal": "apigateway.amazonaws.com", "principal_org_id": null, "qualifier": "", "source_account": null, "source_arn": "arn:aws:execute-api:us-west-2:************:u3vj4dkndh/*/GET/webhooks", "statement_id": "AllowExecutionFromAPIGatewayGET", "statement_id_prefix": ""}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["module.api_gateway.aws_api_gateway_rest_api.rest_api", "module.lambda_function.aws_iam_role.lambda_execution_role", "module.lambda_function.aws_lambda_function.webhooks_function", "module.lambda_function.aws_s3_object.lambda_code_webhooks", "module.lambda_function.data.archive_file.lambda_code_webhooks"]}]}, {"module": "module.lambda_function", "mode": "managed", "type": "aws_lambda_permission", "name": "lambda_api_gw_permissions_POST", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"action": "lambda:InvokeFunction", "event_source_token": null, "function_name": "arn:aws:lambda:us-west-2:************:function:dev-list-products", "function_url_auth_type": null, "id": "AllowExecutionFromAPIGatewayPOST", "principal": "apigateway.amazonaws.com", "principal_org_id": null, "qualifier": "", "source_account": null, "source_arn": "arn:aws:execute-api:us-west-2:************:u3vj4dkndh/*/POST/dev-list-products", "statement_id": "AllowExecutionFromAPIGatewayPOST", "statement_id_prefix": ""}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["module.api_gateway.aws_api_gateway_rest_api.rest_api", "module.lambda_function.aws_iam_role.lambda_execution_role_get", "module.lambda_function.aws_lambda_function.list_products_function", "module.lambda_function.aws_s3_object.lambda_code", "module.lambda_function.data.archive_file.lambda_code_list"]}]}, {"module": "module.lambda_function", "mode": "managed", "type": "aws_lambda_permission", "name": "lambda_api_gw_permissions_POST_checkout", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"action": "lambda:InvokeFunction", "event_source_token": null, "function_name": "arn:aws:lambda:us-west-2:************:function:dev-checkout-products", "function_url_auth_type": null, "id": "AllowExecutionFromAPIGatewayPOST", "principal": "apigateway.amazonaws.com", "principal_org_id": null, "qualifier": "", "source_account": null, "source_arn": "arn:aws:execute-api:us-west-2:************:u3vj4dkndh/*/POST/checkout-products", "statement_id": "AllowExecutionFromAPIGatewayPOST", "statement_id_prefix": ""}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["module.api_gateway.aws_api_gateway_rest_api.rest_api", "module.lambda_function.aws_iam_role.lambda_execution_role", "module.lambda_function.aws_lambda_function.checkout_products_function", "module.lambda_function.aws_s3_object.lambda_code_checkout", "module.lambda_function.data.archive_file.lambda_code_checkout"]}]}, {"module": "module.lambda_function", "mode": "managed", "type": "aws_lambda_permission", "name": "lambda_api_gw_permissions_POST_contact", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"action": "lambda:InvokeFunction", "event_source_token": null, "function_name": "arn:aws:lambda:us-west-2:************:function:dev-contact-email", "function_url_auth_type": null, "id": "AllowExecutionFromAPIGatewayPOST", "principal": "apigateway.amazonaws.com", "principal_org_id": null, "qualifier": "", "source_account": null, "source_arn": "arn:aws:execute-api:us-west-2:************:u3vj4dkndh/*/POST/contact-email", "statement_id": "AllowExecutionFromAPIGatewayPOST", "statement_id_prefix": ""}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["module.api_gateway.aws_api_gateway_rest_api.rest_api", "module.lambda_function.aws_iam_role.lambda_execution_role", "module.lambda_function.aws_lambda_function.contact_email_function", "module.lambda_function.aws_s3_object.lambda_code_contact", "module.lambda_function.data.archive_file.lambda_code_contact"]}]}, {"module": "module.lambda_function", "mode": "managed", "type": "aws_lambda_permission", "name": "lambda_api_gw_permissions_POST_webhooks", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"action": "lambda:InvokeFunction", "event_source_token": null, "function_name": "arn:aws:lambda:us-west-2:************:function:dev-webhooks", "function_url_auth_type": null, "id": "AllowExecutionFromAPIGatewayPOST", "principal": "apigateway.amazonaws.com", "principal_org_id": null, "qualifier": "", "source_account": null, "source_arn": "arn:aws:execute-api:us-west-2:************:u3vj4dkndh/*/POST/webhooks", "statement_id": "AllowExecutionFromAPIGatewayPOST", "statement_id_prefix": ""}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["module.api_gateway.aws_api_gateway_rest_api.rest_api", "module.lambda_function.aws_iam_role.lambda_execution_role", "module.lambda_function.aws_lambda_function.webhooks_function", "module.lambda_function.aws_s3_object.lambda_code_webhooks", "module.lambda_function.data.archive_file.lambda_code_webhooks"]}]}, {"module": "module.lambda_function", "mode": "managed", "type": "aws_lambda_permission", "name": "lambda_api_gw_permissions_checkout", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"action": "lambda:InvokeFunction", "event_source_token": null, "function_name": "arn:aws:lambda:us-west-2:************:function:dev-checkout-products", "function_url_auth_type": null, "id": "AllowExecutionFromAPIGatewayV1", "principal": "apigateway.amazonaws.com", "principal_org_id": null, "qualifier": "", "source_account": null, "source_arn": "arn:aws:execute-api:us-west-2:************:u3vj4dkndh/*/GET/dev-checkout-products", "statement_id": "AllowExecutionFromAPIGatewayV1", "statement_id_prefix": ""}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["module.api_gateway.aws_api_gateway_rest_api.rest_api", "module.lambda_function.aws_iam_role.lambda_execution_role", "module.lambda_function.aws_lambda_function.checkout_products_function", "module.lambda_function.aws_s3_object.lambda_code_checkout", "module.lambda_function.data.archive_file.lambda_code_checkout"]}]}, {"module": "module.lambda_function", "mode": "managed", "type": "aws_lambda_permission", "name": "lambda_api_gw_permissions_contact", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"action": "lambda:InvokeFunction", "event_source_token": null, "function_name": "arn:aws:lambda:us-west-2:************:function:dev-contact-email", "function_url_auth_type": null, "id": "AllowExecutionFromAPIGatewayV1", "principal": "apigateway.amazonaws.com", "principal_org_id": null, "qualifier": "", "source_account": null, "source_arn": "arn:aws:apigateway:us-west-2::/restapis/u3vj4dkndh/*/*", "statement_id": "AllowExecutionFromAPIGatewayV1", "statement_id_prefix": ""}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["module.api_gateway.aws_api_gateway_rest_api.rest_api", "module.lambda_function.aws_iam_role.lambda_execution_role", "module.lambda_function.aws_lambda_function.contact_email_function", "module.lambda_function.aws_s3_object.lambda_code_contact", "module.lambda_function.data.archive_file.lambda_code_contact"]}]}, {"module": "module.lambda_function", "mode": "managed", "type": "aws_lambda_permission", "name": "lambda_api_gw_permissions_webhooks", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"action": "lambda:InvokeFunction", "event_source_token": null, "function_name": "arn:aws:lambda:us-west-2:************:function:dev-webhooks", "function_url_auth_type": null, "id": "AllowExecutionFromAPIGatewayV1", "principal": "apigateway.amazonaws.com", "principal_org_id": null, "qualifier": "", "source_account": null, "source_arn": "arn:aws:apigateway:us-west-2::/restapis/u3vj4dkndh/*/*", "statement_id": "AllowExecutionFromAPIGatewayV1", "statement_id_prefix": ""}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["module.api_gateway.aws_api_gateway_rest_api.rest_api", "module.lambda_function.aws_iam_role.lambda_execution_role", "module.lambda_function.aws_lambda_function.webhooks_function", "module.lambda_function.aws_s3_object.lambda_code_webhooks", "module.lambda_function.data.archive_file.lambda_code_webhooks"]}]}, {"module": "module.lambda_function", "mode": "managed", "type": "aws_s3_bucket", "name": "s3_bucket", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"acceleration_status": "", "acl": null, "arn": "arn:aws:s3:::dev-thealpinestudio-lambda-functions-v1", "bucket": "dev-thealpinestudio-lambda-functions-v1", "bucket_domain_name": "dev-thealpinestudio-lambda-functions-v1.s3.amazonaws.com", "bucket_prefix": "", "bucket_regional_domain_name": "dev-thealpinestudio-lambda-functions-v1.s3.us-west-2.amazonaws.com", "cors_rule": [{"allowed_headers": ["*"], "allowed_methods": ["POST", "GET", "PUT"], "allowed_origins": ["https://test.thealpinestudio.com", "https://thealpinestudio.com"], "expose_headers": [], "max_age_seconds": 3000}], "force_destroy": false, "grant": [{"id": "49f744bf851c1df61a3ea6002103b48141213c5416dac9ce3b0d5bd8a58045e4", "permissions": ["FULL_CONTROL"], "type": "CanonicalUser", "uri": ""}], "hosted_zone_id": "Z3BJ6K6RIION7M", "id": "dev-thealpinestudio-lambda-functions-v1", "lifecycle_rule": [], "logging": [], "object_lock_configuration": [], "object_lock_enabled": false, "policy": "", "region": "us-west-2", "replication_configuration": [], "request_payer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "server_side_encryption_configuration": [{"rule": [{"apply_server_side_encryption_by_default": [{"kms_master_key_id": "", "sse_algorithm": "AES256"}], "bucket_key_enabled": false}]}], "tags": {}, "tags_all": {}, "timeouts": null, "versioning": [{"enabled": false, "mfa_delete": false}], "website": [], "website_domain": null, "website_endpoint": null}, "sensitive_attributes": [], "private": "********************************************************************************************************************************************************************************"}]}, {"module": "module.lambda_function", "mode": "managed", "type": "aws_s3_bucket_cors_configuration", "name": "s3_lambda_cors", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"bucket": "dev-thealpinestudio-lambda-functions-v1", "cors_rule": [{"allowed_headers": ["*"], "allowed_methods": ["GET", "POST", "PUT"], "allowed_origins": ["https://test.thealpinestudio.com", "https://thealpinestudio.com"], "expose_headers": [], "id": "", "max_age_seconds": 3000}], "expected_bucket_owner": "", "id": "dev-thealpinestudio-lambda-functions-v1"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["module.lambda_function.aws_s3_bucket.s3_bucket"]}]}, {"module": "module.lambda_function", "mode": "managed", "type": "aws_s3_bucket_cors_configuration", "name": "s3_list_cors", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"bucket": "dev-thealpinestudio-lambda-functions-v1", "cors_rule": [{"allowed_headers": ["*"], "allowed_methods": ["GET", "POST", "PUT"], "allowed_origins": ["https://test.thealpinestudio.com", "https://thealpinestudio.com"], "expose_headers": [], "id": "", "max_age_seconds": 3000}], "expected_bucket_owner": "", "id": "dev-thealpinestudio-lambda-functions-v1"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["module.lambda_function.aws_s3_bucket.s3_bucket"]}]}, {"module": "module.lambda_function", "mode": "managed", "type": "aws_s3_object", "name": "lambda_code", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"acl": "private", "arn": "arn:aws:s3:::dev-thealpinestudio-lambda-functions-v1/list-products.zip", "bucket": "dev-thealpinestudio-lambda-functions-v1", "bucket_key_enabled": false, "cache_control": "", "checksum_algorithm": null, "checksum_crc32": "", "checksum_crc32c": "", "checksum_sha1": "", "checksum_sha256": "", "content": null, "content_base64": null, "content_disposition": "", "content_encoding": "", "content_language": "", "content_type": "application/zip", "etag": "4d730ecdf497eb56f480b99837782a2f-4", "force_destroy": false, "id": "list-products.zip", "key": "list-products.zip", "kms_key_id": null, "metadata": null, "object_lock_legal_hold_status": "", "object_lock_mode": "", "object_lock_retain_until_date": "", "override_provider": [], "server_side_encryption": "AES256", "source": "./dev-list-products.zip", "source_hash": null, "storage_class": "STANDARD", "tags": null, "tags_all": {}, "version_id": "", "website_redirect": ""}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["module.lambda_function.data.archive_file.lambda_code_list"], "create_before_destroy": true}]}, {"module": "module.lambda_function", "mode": "managed", "type": "aws_s3_object", "name": "lambda_code_checkout", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"acl": "private", "arn": "arn:aws:s3:::dev-thealpinestudio-lambda-functions-v1/dev-checkout-products.zip", "bucket": "dev-thealpinestudio-lambda-functions-v1", "bucket_key_enabled": false, "cache_control": "", "checksum_algorithm": null, "checksum_crc32": "", "checksum_crc32c": "", "checksum_sha1": "", "checksum_sha256": "", "content": null, "content_base64": null, "content_disposition": "", "content_encoding": "", "content_language": "", "content_type": "application/zip", "etag": "a46095d9b446d026bad86f258908c64f-2", "force_destroy": false, "id": "dev-checkout-products.zip", "key": "dev-checkout-products.zip", "kms_key_id": null, "metadata": null, "object_lock_legal_hold_status": "", "object_lock_mode": "", "object_lock_retain_until_date": "", "override_provider": [], "server_side_encryption": "AES256", "source": "./dev-checkout-products.zip", "source_hash": null, "storage_class": "STANDARD", "tags": null, "tags_all": {}, "version_id": "", "website_redirect": ""}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["module.lambda_function.data.archive_file.lambda_code_checkout"], "create_before_destroy": true}]}, {"module": "module.lambda_function", "mode": "managed", "type": "aws_s3_object", "name": "lambda_code_contact", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"acl": "private", "arn": "arn:aws:s3:::dev-thealpinestudio-lambda-functions-v1/contact-email.zip", "bucket": "dev-thealpinestudio-lambda-functions-v1", "bucket_key_enabled": false, "cache_control": "", "checksum_algorithm": null, "checksum_crc32": "", "checksum_crc32c": "", "checksum_sha1": "", "checksum_sha256": "", "content": null, "content_base64": null, "content_disposition": "", "content_encoding": "", "content_language": "", "content_type": "application/zip", "etag": "b33826f9038dca7db8edf8fe1064a327-3", "force_destroy": false, "id": "contact-email.zip", "key": "contact-email.zip", "kms_key_id": null, "metadata": null, "object_lock_legal_hold_status": "", "object_lock_mode": "", "object_lock_retain_until_date": "", "override_provider": [], "server_side_encryption": "AES256", "source": "./dev-contact-email.zip", "source_hash": null, "storage_class": "STANDARD", "tags": null, "tags_all": {}, "version_id": "", "website_redirect": ""}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["module.lambda_function.data.archive_file.lambda_code_contact"], "create_before_destroy": true}]}, {"module": "module.lambda_function", "mode": "managed", "type": "aws_s3_object", "name": "lambda_code_webhooks", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"acl": "private", "arn": "arn:aws:s3:::dev-thealpinestudio-lambda-functions-v1/webhooks.zip", "bucket": "dev-thealpinestudio-lambda-functions-v1", "bucket_key_enabled": false, "cache_control": "", "checksum_algorithm": null, "checksum_crc32": "", "checksum_crc32c": "", "checksum_sha1": "", "checksum_sha256": "", "content": null, "content_base64": null, "content_disposition": "", "content_encoding": "", "content_language": "", "content_type": "application/zip", "etag": "58e10734b81f90826e0f149abf86976d-3", "force_destroy": false, "id": "webhooks.zip", "key": "webhooks.zip", "kms_key_id": null, "metadata": null, "object_lock_legal_hold_status": "", "object_lock_mode": "", "object_lock_retain_until_date": "", "override_provider": [], "server_side_encryption": "AES256", "source": "./dev-webhooks.zip", "source_hash": null, "storage_class": "STANDARD", "tags": null, "tags_all": {}, "version_id": "", "website_redirect": ""}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["module.lambda_function.data.archive_file.lambda_code_webhooks"], "create_before_destroy": true}]}, {"module": "module.website_s3_bucket", "mode": "data", "type": "aws_cloudfront_distribution", "name": "thealpinestudio_s3_distribution_test", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"aliases": ["test.thealpinestudio.com"], "arn": "arn:aws:cloudfront::************:distribution/E333R3CHKXLYGZ", "domain_name": "d33jwd5p0tdk6v.cloudfront.net", "enabled": true, "etag": "E35IGOD7WP4BYQ", "hosted_zone_id": "Z2FDTNDATAQYW2", "id": "E333R3CHKXLYGZ", "in_progress_validation_batches": 0, "last_modified_time": "2025-01-30 19:54:15.623 +0000 UTC", "status": "Deployed", "tags": {}, "web_acl_id": ""}, "sensitive_attributes": []}]}, {"module": "module.website_s3_bucket", "mode": "managed", "type": "aws_s3_bucket", "name": "s3_thealpinestudio_bucket", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"acceleration_status": "", "acl": null, "arn": "arn:aws:s3:::dev-thealpinestudio-hosting-v1", "bucket": "dev-thealpinestudio-hosting-v1", "bucket_domain_name": "dev-thealpinestudio-hosting-v1.s3.amazonaws.com", "bucket_prefix": "", "bucket_regional_domain_name": "dev-thealpinestudio-hosting-v1.s3.us-west-2.amazonaws.com", "cors_rule": [{"allowed_headers": [], "allowed_methods": ["POST", "GET", "PUT"], "allowed_origins": ["https://test.thealpinestudio.com", "https://d33jwd5p0tdk6v.cloudfront.net"], "expose_headers": [], "max_age_seconds": 0}, {"allowed_headers": ["*"], "allowed_methods": ["POST", "GET", "PUT"], "allowed_origins": ["https://test.thealpinestudio.com", "https://d33jwd5p0tdk6v.cloudfront.net"], "expose_headers": ["ETag"], "max_age_seconds": 3000}], "force_destroy": false, "grant": [{"id": "", "permissions": ["READ"], "type": "Group", "uri": "http://acs.amazonaws.com/groups/global/AllUsers"}, {"id": "49f744bf851c1df61a3ea6002103b48141213c5416dac9ce3b0d5bd8a58045e4", "permissions": ["FULL_CONTROL"], "type": "CanonicalUser", "uri": ""}], "hosted_zone_id": "Z3BJ6K6RIION7M", "id": "dev-thealpinestudio-hosting-v1", "lifecycle_rule": [], "logging": [], "object_lock_configuration": [], "object_lock_enabled": false, "policy": "{\"Statement\":[{\"Action\":[\"s3:GetObject\",\"s3:PutObject\"],\"Effect\":\"Allow\",\"Principal\":\"*\",\"Resource\":\"arn:aws:s3:::dev-thealpinestudio-hosting-v1/*\",\"Sid\":\"PublicRead\"}],\"Version\":\"2012-10-17\"}", "region": "us-west-2", "replication_configuration": [], "request_payer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "server_side_encryption_configuration": [{"rule": [{"apply_server_side_encryption_by_default": [{"kms_master_key_id": "", "sse_algorithm": "AES256"}], "bucket_key_enabled": false}]}], "tags": {"Environment": "development"}, "tags_all": {"Environment": "development"}, "timeouts": null, "versioning": [{"enabled": false, "mfa_delete": false}], "website": [{"error_document": "error.html", "index_document": "index.html", "redirect_all_requests_to": "", "routing_rules": ""}], "website_domain": "s3-website-us-west-2.amazonaws.com", "website_endpoint": "dev-thealpinestudio-hosting-v1.s3-website-us-west-2.amazonaws.com"}, "sensitive_attributes": [], "private": "********************************************************************************************************************************************************************************"}]}, {"module": "module.website_s3_bucket", "mode": "managed", "type": "aws_s3_bucket_acl", "name": "s3_thealpinestudio_website_bucket_acl", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"access_control_policy": [{"grant": [{"grantee": [{"display_name": "", "email_address": "", "id": "", "type": "Group", "uri": "http://acs.amazonaws.com/groups/global/AllUsers"}], "permission": "READ"}, {"grantee": [{"display_name": "ariel", "email_address": "", "id": "49f744bf851c1df61a3ea6002103b48141213c5416dac9ce3b0d5bd8a58045e4", "type": "CanonicalUser", "uri": ""}], "permission": "FULL_CONTROL"}], "owner": [{"display_name": "ariel", "id": "49f744bf851c1df61a3ea6002103b48141213c5416dac9ce3b0d5bd8a58045e4"}]}], "acl": "public-read", "bucket": "dev-thealpinestudio-hosting-v1", "expected_bucket_owner": "", "id": "dev-thealpinestudio-hosting-v1,public-read"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["module.website_s3_bucket.aws_s3_bucket.s3_thealpinestudio_bucket", "module.website_s3_bucket.aws_s3_bucket_ownership_controls.s3_thealpinestudio_ownership", "module.website_s3_bucket.aws_s3_bucket_public_access_block.s3_thealpinestudio_public_block"]}]}, {"module": "module.website_s3_bucket", "mode": "managed", "type": "aws_s3_bucket_cors_configuration", "name": "s3_cors", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"bucket": "dev-thealpinestudio-hosting-v1", "cors_rule": [{"allowed_headers": ["*"], "allowed_methods": ["GET", "POST", "PUT"], "allowed_origins": ["https://d33jwd5p0tdk6v.cloudfront.net", "https://test.thealpinestudio.com"], "expose_headers": ["ETag"], "id": "", "max_age_seconds": 3000}, {"allowed_headers": [], "allowed_methods": ["GET", "POST", "PUT"], "allowed_origins": ["https://d33jwd5p0tdk6v.cloudfront.net", "https://test.thealpinestudio.com"], "expose_headers": [], "id": "", "max_age_seconds": 0}], "expected_bucket_owner": "", "id": "dev-thealpinestudio-hosting-v1"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["module.website_s3_bucket.aws_s3_bucket.s3_thealpinestudio_bucket", "module.website_s3_bucket.data.aws_cloudfront_distribution.thealpinestudio_s3_distribution_test"]}]}, {"module": "module.website_s3_bucket", "mode": "managed", "type": "aws_s3_bucket_ownership_controls", "name": "s3_thealpinestudio_ownership", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"bucket": "dev-thealpinestudio-hosting-v1", "id": "dev-thealpinestudio-hosting-v1", "rule": [{"object_ownership": "BucketOwnerPreferred"}]}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["module.website_s3_bucket.aws_s3_bucket.s3_thealpinestudio_bucket"]}]}, {"module": "module.website_s3_bucket", "mode": "managed", "type": "aws_s3_bucket_policy", "name": "s3_thealpinestudio_policy", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"bucket": "dev-thealpinestudio-hosting-v1", "id": "dev-thealpinestudio-hosting-v1", "policy": "{\"Statement\":[{\"Action\":[\"s3:GetObject\",\"s3:PutObject\"],\"Effect\":\"Allow\",\"Principal\":\"*\",\"Resource\":\"arn:aws:s3:::dev-thealpinestudio-hosting-v1/*\",\"Sid\":\"PublicRead\"}],\"Version\":\"2012-10-17\"}"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["module.website_s3_bucket.aws_s3_bucket.s3_thealpinestudio_bucket"]}]}, {"module": "module.website_s3_bucket", "mode": "managed", "type": "aws_s3_bucket_public_access_block", "name": "s3_thealpinestudio_public_block", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"block_public_acls": false, "block_public_policy": false, "bucket": "dev-thealpinestudio-hosting-v1", "id": "dev-thealpinestudio-hosting-v1", "ignore_public_acls": false, "restrict_public_buckets": false}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["module.website_s3_bucket.aws_s3_bucket.s3_thealpinestudio_bucket"]}]}, {"module": "module.website_s3_bucket", "mode": "managed", "type": "aws_s3_bucket_website_configuration", "name": "s3_thealpinestudio_website_bucket", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"bucket": "dev-thealpinestudio-hosting-v1", "error_document": [{"key": "error.html"}], "expected_bucket_owner": "", "id": "dev-thealpinestudio-hosting-v1", "index_document": [{"suffix": "index.html"}], "redirect_all_requests_to": [], "routing_rule": [], "routing_rules": "", "website_domain": "s3-website-us-west-2.amazonaws.com", "website_endpoint": "dev-thealpinestudio-hosting-v1.s3-website-us-west-2.amazonaws.com"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["module.website_s3_bucket.aws_s3_bucket.s3_thealpinestudio_bucket"]}]}], "check_results": null}