#!/bin/bash
# Script to start the development server with environment variables

# Set environment variables for development
export NODE_ENV=development
export REACT_APP_ENVIRONMENT=development
export REACT_APP_CART_API_ROUTE=https://api-dev.thealpinestudio.com/dev-checkout-products
export REACT_APP_PRODUCT_API_ROUTE=https://api-dev.thealpinestudio.com/dev-list-products
export REACT_APP_CONTACT_API_ROUTE=https://api-dev.thealpinestudio.com/dev-contact-email
export REACT_APP_WEBHOOKS_API_ROUTE=https://api-dev.thealpinestudio.com/dev-webhooks
export REACT_APP_STRIPE_PUBLIC_KEY=pk_test_51NirT5H1KC3YYzQbwoT6Tewe6rFzhf3e5xmWrgIw75V01q126czeifShPGVOSf00K8Pblw7FUA5SaBEXfW5VnXb700uS8rSOyL
export REACT_APP_S3_BUCKET=dev-thealpinestudio-hosting

# Navigate to client directory
cd client

# Start the development server
npm start
