name: Dev Environment Deployment

on:
  push:
    branches:
      - dev
      - develop
      - feature/*
    paths:
      - "lambda/dev**/**"
      - "terraform/development/**"
      - "client/**"
      - ".github/workflows/dev-deployment.yml"
  pull_request:
    branches:
      - dev
      - develop
    types: [opened, synchronize, reopened]
  workflow_dispatch:
    inputs:
      environment:
        description: "Environment to deploy to"
        required: true
        default: "dev"
        type: choice
        options:
          - dev
      tag:
        description: "Tag to apply to the deployment"
        required: false
        type: string

jobs:
  check_branch:
    runs-on: ubuntu-latest
    outputs:
      should_deploy: ${{ steps.check.outputs.should_deploy }}
      environment: ${{ steps.check.outputs.environment }}
    steps:
      - name: Check branch and determine environment
        id: check
        run: |
          if [[ "${{ github.event_name }}" == "workflow_dispatch" ]]; then
            echo "should_deploy=true" >> $GITHUB_OUTPUT
            echo "environment=${{ github.event.inputs.environment }}" >> $GITHUB_OUTPUT
          elif [[ "${{ github.event_name }}" == "pull_request" ]]; then
            echo "should_deploy=false" >> $GITHUB_OUTPUT
            echo "environment=dev" >> $GITHUB_OUTPUT
          elif [[ "${{ github.ref }}" == "refs/heads/dev" || "${{ github.ref }}" == "refs/heads/develop" ]]; then
            echo "should_deploy=true" >> $GITHUB_OUTPUT
            echo "environment=dev" >> $GITHUB_OUTPUT
          elif [[ "${{ github.ref }}" =~ ^refs/heads/feature/.* ]]; then
            echo "should_deploy=false" >> $GITHUB_OUTPUT
            echo "environment=dev" >> $GITHUB_OUTPUT
          else
            echo "should_deploy=false" >> $GITHUB_OUTPUT
            echo "environment=dev" >> $GITHUB_OUTPUT
          fi

  deploy:
    needs: check_branch
    if: needs.check_branch.outputs.should_deploy == 'true'
    runs-on: ubuntu-latest
    environment: ${{ needs.check_branch.outputs.environment }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Set up Go
        uses: actions/setup-go@v4
        with:
          go-version: "1.20"

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v2
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: us-west-2

      - name: Create S3 bucket for Terraform state if it doesn't exist
        run: |
          aws s3api head-bucket --bucket dev-thealpinestudio-backend-tf 2>/dev/null || \
          aws s3 mb s3://dev-thealpinestudio-backend-tf --region us-west-2

      - name: Build Lambda Functions
        run: |
          # Build devListProducts
          cd lambda/dev/ListProducts
          GOOS=linux GOARCH=amd64 go build -o bootstrap listProducts.go
          zip dev-list-products.zip bootstrap
          aws s3 cp dev-list-products.zip s3://dev-thealpinestudio-lambda-functions-v1/list-products.zip
          cd ../..

          # Build devCheckoutProducts
          cd lambda/dev/CheckoutProducts
          GOOS=linux GOARCH=amd64 go build -o bootstrap checkoutProducts.go
          zip dev-checkout-products.zip bootstrap
          aws s3 cp dev-checkout-products.zip s3://dev-thealpinestudio-lambda-functions-v1/checkout-products.zip
          cd ../..

          # Build devContactEmail
          cd lambda/dev/ContactEmail
          GOOS=linux GOARCH=amd64 go build -o bootstrap contactEmail.go
          zip dev-contact-email.zip bootstrap
          aws s3 cp dev-contact-email.zip s3://dev-thealpinestudio-lambda-functions-v1/contact-email.zip
          cd ../..

          # Build devWebhooks
          cd lambda/dev/Webhooks
          GOOS=linux GOARCH=amd64 go build -o bootstrap webhooks.go
          zip dev-webhooks.zip bootstrap
          aws s3 cp dev-webhooks.zip s3://dev-thealpinestudio-lambda-functions-v1/webhooks.zip
          cd ../..

      - name: Setup Terraform
        uses: hashicorp/setup-terraform@v2
        with:
          terraform_version: 1.5.0

      - name: Create terraform.tfvars file
        run: |
          cat > terraform/development/dev-env/terraform.tfvars << EOF
          infra_env = "development"
          access_key = "${{ secrets.AWS_ACCESS_KEY_ID }}"
          secret_key = "${{ secrets.AWS_SECRET_ACCESS_KEY }}"
          account_id = "${{ secrets.AWS_ACCOUNT_ID }}"
          zone_id = "${{ secrets.ROUTE53_ZONE_ID }}"
          domain_name = "test.thealpinestudio.com"
          hosting_bucket_name = "dev-thealpinestudio-hosting"
          list_products_function_name = "dev-list-products"
          api_gateway_id = "${{ secrets.DEV_API_GATEWAY_ID }}"
          api_gateway_account_id = "${{ secrets.AWS_ACCOUNT_ID }}"
          api_gateway_region = "us-west-2"
          stripe_secret_key = "${{ secrets.STRIPE_SECRET_KEY }}"
          stripe_endpoint_secret = "${{ secrets.STRIPE_ENDPOINT_SECRET }}"
          resend_api_key = "${{ secrets.RESEND_API_KEY }}"
          frontend_url = "https://test.thealpinestudio.com"
          dynamodb_orders_table = "dev-orders"
          EOF

      - name: Terraform Init
        run: |
          cd terraform/development/dev-env
          terraform init

      - name: Terraform Plan
        run: |
          cd terraform/development/dev-env
          terraform plan -var-file=terraform.tfvars -out=tfplan

      - name: Terraform Apply
        run: |
          cd terraform/development/dev-env
          terraform apply -auto-approve tfplan

      - name: Test API Endpoints
        run: |
          echo "Testing dev-list-products endpoint..."
          ENDPOINT=$(terraform -chdir=terraform/development/dev-env output -raw api_endpoint_url)
          curl -s "${ENDPOINT}/dev-list-products" | grep -q "products" && echo "Success!" || echo "Failed!"

      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: "18"
          cache: "npm"
          cache-dependency-path: client/package-lock.json

      - name: Build Frontend
        run: |
          cd client
          npm ci

          # Set environment variables for development
          export NODE_ENV=development
          export REACT_APP_ENVIRONMENT=development
          export REACT_APP_CART_API_ROUTE="${ENDPOINT}/dev-checkout-products"
          export REACT_APP_PRODUCT_API_ROUTE="${ENDPOINT}/dev-list-products"
          export REACT_APP_CONTACT_API_ROUTE="${ENDPOINT}/dev-contact-email"
          export REACT_APP_WEBHOOKS_API_ROUTE="${ENDPOINT}/dev-webhooks"
          export REACT_APP_STRIPE_PUBLIC_KEY="${{ secrets.STRIPE_PUBLIC_KEY }}"
          export REACT_APP_S3_BUCKET="dev-thealpinestudio-hosting"

          # Build for development
          npm run build:dev

      - name: Deploy Frontend to S3
        run: |
          aws s3 cp client/build/ s3://test.thealpinestudio.com-v1/ --recursive --region us-west-2

          # Invalidate CloudFront cache
          aws cloudfront create-invalidation --distribution-id E333R3CHKXLYGZ --paths "/*" --region us-west-2

      - name: Create and push tag
        if: ${{ github.event.inputs.tag != '' || github.ref == 'refs/heads/dev' || github.ref == 'refs/heads/develop' }}
        run: |
          # Set up Git user
          git config --global user.name "GitHub Actions"
          git config --global user.email "<EMAIL>"

          # Determine tag name
          if [[ "${{ github.event.inputs.tag }}" != "" ]]; then
            TAG_NAME="${{ github.event.inputs.tag }}"
          else
            # Generate tag based on branch and date
            BRANCH_NAME=${GITHUB_REF#refs/heads/}
            DATE=$(date +'%Y%m%d-%H%M%S')
            TAG_NAME="${BRANCH_NAME}-${DATE}"
          fi

          # Create and push tag
          git tag -a "${TAG_NAME}" -m "Deployment from GitHub Actions workflow"
          git push origin "${TAG_NAME}"

          echo "Created and pushed tag: ${TAG_NAME}"
