// Development environment configuration

// Base API URL for development (using actual AWS API Gateway)
const DEV_API_BASE =
  "https://u3vj4dkndh.execute-api.us-west-2.amazonaws.com/test";

export const configLogin = {
  API_ENDPOINT_LOGIN: `${DEV_API_BASE}/dev/auth-login`,
};

export const configPass = {
  API_ENDPOINT_NEWPASSWORD: `${DEV_API_BASE}/dev/change-password`,
};

export const configVerify = {
  API_ENDPOINT_VERIFY: `${DEV_API_BASE}/dev/verify-mfa`,
};

export const configPreSignup = {
  API_ENDPOINT_PRESIGNUP: `${DEV_API_BASE}/dev/presign`,
};

export const configPostConfirmation = {
  API_ENDPOINT_POSTCONFIRM: `${DEV_API_BASE}/dev/post-confirmation`,
};

export const configSignup = {
  API_ENDPOINT_SIGNUP: `${DEV_API_BASE}/dev/signup`,
};

export const configConfirmSignup = {
  API_ENDPOINT_CONFIRMSIGNUP: `${DEV_API_BASE}/dev/confirm-signup`,
};

export const configTotp = {
  API_ENDPOINT_TOTP: `${DEV_API_BASE}/dev/totp-secret`,
};

export const configRefreshToken = {
  API_ENDPOINT_REFRESH: `${DEV_API_BASE}/dev/refresh-token`,
};

export const configUploadProduct = {
  API_ENDPOINT_UPLOAD_PRODUCT: `${DEV_API_BASE}/dev/products`,
};

export const configFetchProducts = {
  API_ENDPOINT_FETCH_PRODUCTS: `${DEV_API_BASE}/dev/admin-list-products`,
};

export const configMarkProductAsSold = {
  API_ENDPOINT_MARK_PRODUCT_AS_SOLD: `${DEV_API_BASE}/dev/sold-product`,
};

export const configDeleteProduct = {
  API_ENDPOINT_DELETE_PRODUCT: (id: string) =>
    `${DEV_API_BASE}/dev/products/${id}`,
};

export const configEditProduct = {
  API_ENDPOINT_EDIT_PRODUCT: (id: string) =>
    `${DEV_API_BASE}/dev/products/${id}`,
};

export const configFetchProductById = {
  API_ENDPOINT_FETCH_PRODUCT_BY_ID: (id: string) =>
    `${DEV_API_BASE}/dev/products/${id}`,
};

export const configPreSignUrl = {
  API_ENDPOINT_PRESIGNURL: `${DEV_API_BASE}/dev/presigned-url`,
};

// Client-facing API endpoints
export const configTestListProducts = {
  API_ENPOINT_TEST_LIST_PRODUCTS: `${DEV_API_BASE}/dev-list-products`,
};

export const configStripeCheckout = {
  API_ENDPOINT_STRIPE_CHECKOUT: `${DEV_API_BASE}/dev-checkout-products`,
};

export const configContactForm = {
  RESEND_API_CONTACT_FORM: `${DEV_API_BASE}/dev-contact-email`,
};
